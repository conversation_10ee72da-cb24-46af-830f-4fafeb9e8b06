using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using SuperBeerPong.Core.Core;
using SuperBeerPong.Core.Core.Interfaces;
using System;

namespace SuperBeerPong.Core.UI
{
    /// <summary>
    /// Main menu screen implementation
    /// </summary>
    public class MainMenu : IMenuScreen
    {
        private IInputHandler inputHandler;
        private int selectedOption;
        private bool isActive;

        // Events
        public event Action<MenuState> NavigationRequested;
        public event Action<object> ActionCompleted;

        // Properties
        public MenuState State => MenuState.Main;
        public bool IsActive { get; set; }

        public MainMenu(IInputHandler inputHandler)
        {
            this.inputHandler = inputHandler;
            selectedOption = 0;
        }

        /// <summary>
        /// Initialize the main menu
        /// </summary>
        public void Initialize()
        {
            selectedOption = 0;
            isActive = false;
        }

        /// <summary>
        /// Update main menu logic
        /// </summary>
        public void Update(GameTime gameTime)
        {
            if (!IsActive) return;

            // Menu navigation is handled in HandleInput
        }

        /// <summary>
        /// Handle input for main menu
        /// </summary>
        public void HandleInput(GameTime gameTime)
        {
            if (!IsActive) return;

            // Handle menu navigation
            int navigation = inputHandler.GetMenuNavigation();
            if (navigation != 0)
            {
                selectedOption += navigation;
                
                // Wrap around menu options
                if (selectedOption < 0)
                    selectedOption = GameConfig.MainMenuOptionCount - 1;
                else if (selectedOption >= GameConfig.MainMenuOptionCount)
                    selectedOption = 0;
            }

            // Handle menu selection
            if (inputHandler.IsMenuSelectTriggered())
            {
                HandleMenuSelection();
            }
        }

        /// <summary>
        /// Render the main menu
        /// </summary>
        public void Draw(SpriteBatch spriteBatch, SpriteFont font)
        {
            if (!IsActive) return;

            var viewport = spriteBatch.GraphicsDevice.Viewport;

            // Draw title
            string title = "SUPER BEER PONG";
            Vector2 titleSize = font.MeasureString(title);
            Vector2 titlePos = new Vector2((viewport.Width - titleSize.X) / 2, 100);
            spriteBatch.DrawString(font, title, titlePos, GameConfig.MenuTitleColor);

            // Draw menu options
            Vector2 menuStart = new Vector2(viewport.Width / 2, 220);
            
            for (int i = 0; i < GameConfig.MainMenuOptions.Length; i++)
            {
                string option = GameConfig.MainMenuOptions[i];
                Vector2 optionSize = font.MeasureString(option);
                Vector2 optionPos = new Vector2(
                    menuStart.X - optionSize.X / 2,
                    menuStart.Y + i * GameConfig.MenuOptionSpacing
                );

                Color optionColor = (i == selectedOption) ? 
                    GameConfig.MenuSelectedColor : GameConfig.MenuNormalColor;

                spriteBatch.DrawString(font, option, optionPos, optionColor);

                // Draw selection indicator
                if (i == selectedOption)
                {
                    string indicator = "> ";
                    Vector2 indicatorPos = new Vector2(
                        optionPos.X - font.MeasureString(indicator).X,
                        optionPos.Y
                    );
                    spriteBatch.DrawString(font, indicator, indicatorPos, GameConfig.MenuSelectedColor);
                }
            }

            // Draw instructions
            string instructions = "Use ARROW KEYS to navigate, ENTER to select, ESC to exit";
            Vector2 instructionsSize = font.MeasureString(instructions);
            Vector2 instructionsPos = new Vector2(
                (viewport.Width - instructionsSize.X) / 2,
                viewport.Height - 100
            );
            spriteBatch.DrawString(font, instructions, instructionsPos, Color.Gray);
        }

        /// <summary>
        /// Called when this screen becomes active
        /// </summary>
        public void OnEnter()
        {
            IsActive = true;
            selectedOption = 0; // Reset selection when entering
        }

        /// <summary>
        /// Called when this screen becomes inactive
        /// </summary>
        public void OnExit()
        {
            IsActive = false;
        }

        /// <summary>
        /// Handle menu selection based on current option
        /// </summary>
        private void HandleMenuSelection()
        {
            switch (selectedOption)
            {
                case 0: // Story Mode
                    ActionCompleted?.Invoke(GameModeType.StoryMode);
                    break;

                case 1: // Player vs Computer
                    ActionCompleted?.Invoke(GameModeType.PlayerVsComputer);
                    break;

                case 2: // Player vs Player
                    ActionCompleted?.Invoke(GameModeType.PlayerVsPlayer);
                    break;

                case 3: // Exit
                    ActionCompleted?.Invoke("Exit");
                    break;
            }
        }

        /// <summary>
        /// Get the currently selected menu option
        /// </summary>
        public int GetSelectedOption()
        {
            return selectedOption;
        }

        /// <summary>
        /// Set the selected menu option
        /// </summary>
        public void SetSelectedOption(int option)
        {
            if (option >= 0 && option < GameConfig.MainMenuOptionCount)
            {
                selectedOption = option;
            }
        }

        /// <summary>
        /// Get the text of the currently selected option
        /// </summary>
        public string GetSelectedOptionText()
        {
            if (selectedOption >= 0 && selectedOption < GameConfig.MainMenuOptions.Length)
            {
                return GameConfig.MainMenuOptions[selectedOption];
            }
            return "";
        }

        /// <summary>
        /// Check if a specific option is selected
        /// </summary>
        public bool IsOptionSelected(int optionIndex)
        {
            return selectedOption == optionIndex;
        }

        /// <summary>
        /// Reset the menu to default state
        /// </summary>
        public void Reset()
        {
            selectedOption = 0;
        }
    }
}
