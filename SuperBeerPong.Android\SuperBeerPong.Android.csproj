<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0-android</TargetFramework>
    <SupportedOSPlatformVersion>29</SupportedOSPlatformVersion>
    <ApplicationId>com.companyname.SuperBeerPong</ApplicationId>
    <ApplicationVersion>1</ApplicationVersion>
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <AssemblyName>SuperBeerPong</AssemblyName>
  </PropertyGroup>
  <ItemGroup>
    <MonoGameContentReference Include="..\SuperBeerPong.Core\Content\SuperBeerPong.mgcb">
      <Link>Content\SuperBeerPong.mgcb</Link>
    </MonoGameContentReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SuperBeerPong.Core\SuperBeerPong.Core.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="MonoGame.Content.Builder.Task" Version="3.8.*" />
    <PackageReference Include="MonoGame.Framework.Android" Version="3.8.*" />
  </ItemGroup>
  <Target Name="RestoreDotnetTools" BeforeTargets="CollectPackageReferences">
    <Message Text="Restoring dotnet tools (this might take a while depending on your internet speed and should only happen upon building your project for the first time, or after upgrading MonoGame, or clearing your nuget cache)" Importance="High" />
    <Exec Command="dotnet tool restore" />
  </Target>
</Project>