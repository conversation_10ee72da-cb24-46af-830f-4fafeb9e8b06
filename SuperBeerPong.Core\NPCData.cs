using System;
using System.Collections.Generic;
using Microsoft.Xna.Framework;

namespace SuperBeerPong.Core
{
    /// <summary>
    /// Base class for all NPCs in story mode
    /// </summary>
    public abstract class NPC
    {
        public string Name { get; protected set; }
        public string Description { get; protected set; }
        public int DifficultyLevel { get; protected set; } // 1-10
        public string SpecialAbility { get; protected set; }
        public string ProfileImageName { get; protected set; } // Image file name without extension

        protected NPC(string name, string description, int difficultyLevel, string specialAbility, string profileImageName)
        {
            Name = name;
            Description = description;
            DifficultyLevel = difficultyLevel;
            SpecialAbility = specialAbility;
            ProfileImageName = profileImageName;
        }
        
        /// <summary>
        /// Apply NPC-specific modifications to AI accuracy
        /// </summary>
        public abstract Vector2 ModifyAIAccuracy(Vector2 baseTarget, float intoxication, Random random);
        
        /// <summary>
        /// Apply NPC-specific modifications to intoxication effects
        /// </summary>
        public abstract float ModifyIntoxication(float baseIntoxication, bool isDecay);
        
        /// <summary>
        /// Apply NPC-specific modifications to player line speed
        /// </summary>
        public abstract float ModifyPlayerLineSpeed(float baseSpeed, float playerIntoxication);
        
        /// <summary>
        /// Check if NPC's special ability should trigger this turn
        /// </summary>
        public abstract bool ShouldTriggerSpecialAbility(Random random);
    }
    
    /// <summary>
    /// Beginner NPCs with reduced accuracy
    /// </summary>
    public class BeginnerNPC : NPC
    {
        private readonly float accuracyReduction;

        public BeginnerNPC(string name, string description, int difficultyLevel, string profileImageName, float accuracyReduction = 50f)
            : base(name, description, difficultyLevel, "Novice Player - Reduced accuracy", profileImageName)
        {
            this.accuracyReduction = accuracyReduction;
        }
        
        public override Vector2 ModifyAIAccuracy(Vector2 baseTarget, float intoxication, Random random)
        {
            // Add extra inaccuracy for beginner NPCs
            float extraInaccuracy = accuracyReduction;
            float offsetX = (float)(random.NextDouble() - 0.5) * 2f * extraInaccuracy;
            float offsetY = (float)(random.NextDouble() - 0.5) * 2f * extraInaccuracy;
            return baseTarget + new Vector2(offsetX, offsetY);
        }
        
        public override float ModifyIntoxication(float baseIntoxication, bool isDecay)
        {
            return baseIntoxication; // No modification
        }
        
        public override float ModifyPlayerLineSpeed(float baseSpeed, float playerIntoxication)
        {
            return baseSpeed; // No modification
        }
        
        public override bool ShouldTriggerSpecialAbility(Random random)
        {
            return false; // No special ability triggers
        }
    }
    
    /// <summary>
    /// Intermediate NPCs with occasional special abilities
    /// </summary>
    public class IntermediateNPC : NPC
    {
        private readonly string abilityType;
        private readonly float abilityChance;

        public IntermediateNPC(string name, string description, int difficultyLevel, string abilityType, string profileImageName, float abilityChance = 0.2f)
            : base(name, description, difficultyLevel, abilityType, profileImageName)
        {
            this.abilityType = abilityType;
            this.abilityChance = abilityChance;
        }
        
        public override Vector2 ModifyAIAccuracy(Vector2 baseTarget, float intoxication, Random random)
        {
            if (abilityType == "Lucky Shot" && ShouldTriggerSpecialAbility(random))
            {
                // Perfect accuracy when lucky shot triggers
                return baseTarget;
            }
            
            // Normal accuracy with slight improvement
            float improvement = 20f;
            float maxInaccuracy = Math.Max(0f, (intoxication * 200f) - improvement);
            float offsetX = (float)(random.NextDouble() - 0.5) * 2f * maxInaccuracy;
            float offsetY = (float)(random.NextDouble() - 0.5) * 2f * maxInaccuracy;
            return baseTarget + new Vector2(offsetX, offsetY);
        }
        
        public override float ModifyIntoxication(float baseIntoxication, bool isDecay)
        {
            if (abilityType == "Steady Hands" && !isDecay)
            {
                // Reduce intoxication gain by 50%
                return baseIntoxication * 0.5f;
            }
            return baseIntoxication;
        }
        
        public override float ModifyPlayerLineSpeed(float baseSpeed, float playerIntoxication)
        {
            return baseSpeed; // No modification for intermediate NPCs
        }
        
        public override bool ShouldTriggerSpecialAbility(Random random)
        {
            return random.NextDouble() < abilityChance;
        }
    }
    
    /// <summary>
    /// Advanced NPCs with powerful abilities
    /// </summary>
    public class AdvancedNPC : NPC
    {
        private readonly string abilityType;
        private readonly float abilityStrength;

        public AdvancedNPC(string name, string description, int difficultyLevel, string abilityType, string profileImageName, float abilityStrength = 1.0f)
            : base(name, description, difficultyLevel, abilityType, profileImageName)
        {
            this.abilityType = abilityType;
            this.abilityStrength = abilityStrength;
        }
        
        public override Vector2 ModifyAIAccuracy(Vector2 baseTarget, float intoxication, Random random)
        {
            // High accuracy with minimal intoxication effects
            float maxInaccuracy = intoxication * 100f; // Reduced from 200f
            float offsetX = (float)(random.NextDouble() - 0.5) * 2f * maxInaccuracy;
            float offsetY = (float)(random.NextDouble() - 0.5) * 2f * maxInaccuracy;
            return baseTarget + new Vector2(offsetX, offsetY);
        }
        
        public override float ModifyIntoxication(float baseIntoxication, bool isDecay)
        {
            if (abilityType == "Rapid Recovery" && isDecay)
            {
                // Faster intoxication decay
                return baseIntoxication * (1f + abilityStrength);
            }
            return baseIntoxication;
        }
        
        public override float ModifyPlayerLineSpeed(float baseSpeed, float playerIntoxication)
        {
            if (abilityType == "Pressure Player")
            {
                // Increase player's line speed (make it harder)
                return baseSpeed * (1f + abilityStrength * 0.5f);
            }
            return baseSpeed;
        }
        
        public override bool ShouldTriggerSpecialAbility(Random random)
        {
            return true; // Advanced abilities are always active
        }
    }
    
    /// <summary>
    /// Final boss NPC with immunity to intoxication
    /// </summary>
    public class BossNPC : NPC
    {
        public BossNPC(string name, string description, string profileImageName)
            : base(name, description, 10, "Iron Liver - Immune to intoxication", profileImageName)
        {
        }
        
        public override Vector2 ModifyAIAccuracy(Vector2 baseTarget, float intoxication, Random random)
        {
            // Boss is immune to intoxication effects on accuracy
            float baseInaccuracy = 15f; // Only slight base inaccuracy
            float offsetX = (float)(random.NextDouble() - 0.5) * 2f * baseInaccuracy;
            float offsetY = (float)(random.NextDouble() - 0.5) * 2f * baseInaccuracy;
            return baseTarget + new Vector2(offsetX, offsetY);
        }
        
        public override float ModifyIntoxication(float baseIntoxication, bool isDecay)
        {
            if (!isDecay)
            {
                // Boss doesn't gain intoxication
                return 0f;
            }
            return baseIntoxication;
        }
        
        public override float ModifyPlayerLineSpeed(float baseSpeed, float playerIntoxication)
        {
            // Boss applies constant pressure
            return baseSpeed * 1.3f;
        }
        
        public override bool ShouldTriggerSpecialAbility(Random random)
        {
            return true; // Boss ability always active
        }
    }
    
    /// <summary>
    /// Factory class to create and manage all NPCs
    /// </summary>
    public static class NPCFactory
    {
        private static readonly Dictionary<int, NPC> npcs = new Dictionary<int, NPC>();
        
        static NPCFactory()
        {
            InitializeNPCs();
        }
        
        private static void InitializeNPCs()
        {
            // Beginner NPCs (1-3) - Use profile-brian for now, can be customized later
            npcs[1] = new BeginnerNPC("Rookie Rick", "A newcomer to beer pong who's still learning the ropes.", 1, "profile-tony", 60f);
            npcs[2] = new BeginnerNPC("Casual Casey", "Plays for fun but lacks serious technique.", 2, "profile-brian", 45f);
            npcs[3] = new BeginnerNPC("Weekend Warrior", "Only plays at parties, inconsistent form.", 3, "profile-brian", 35f);

            // Intermediate NPCs (4-6)
            npcs[4] = new IntermediateNPC("Lucky Luke", "Sometimes hits impossible shots through pure luck.", 4, "Lucky Shot", "profile-brian", 0.25f);
            npcs[5] = new IntermediateNPC("Steady Steve", "Known for maintaining composure under pressure.", 5, "Steady Hands", "profile-brian", 0.3f);
            npcs[6] = new IntermediateNPC("Clutch Clara", "Performs better when the stakes are high.", 6, "Lucky Shot", "profile-brian", 0.2f);

            // Advanced NPCs (7-9)
            npcs[7] = new AdvancedNPC("Rapid Rob", "Recovers from drinking effects faster than most.", 7, "Rapid Recovery", "profile-brian", 1.5f);
            npcs[8] = new AdvancedNPC("Pressure Pete", "Makes opponents nervous with intimidating presence.", 8, "Pressure Player", "profile-brian", 1.0f);
            npcs[9] = new AdvancedNPC("Veteran Vince", "Years of experience show in every shot.", 9, "Rapid Recovery", "profile-brian", 2.0f);

            // Final Boss (10)
            npcs[10] = new BossNPC("Iron Liver Larry", "The legendary champion who never gets drunk.", "profile-brian");
        }
        
        public static NPC GetNPC(int npcNumber)
        {
            return npcs.TryGetValue(npcNumber, out NPC npc) ? npc : null;
        }
        
        public static Dictionary<int, NPC> GetAllNPCs()
        {
            return new Dictionary<int, NPC>(npcs);
        }
        
        public static int GetTotalNPCCount()
        {
            return npcs.Count;
        }
    }
}
