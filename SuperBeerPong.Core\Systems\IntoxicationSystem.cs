using Microsoft.Xna.Framework;
using SuperBeerPong.Core.Core;
using System;

namespace SuperBeerPong.Core.Systems
{
    /// <summary>
    /// Manages intoxication levels and their effects on gameplay
    /// </summary>
    public class IntoxicationSystem
    {
        private float playerIntoxication;
        private float opponentIntoxication;

        // Events for intoxication changes
        public event Action<float> PlayerIntoxicationChanged;
        public event Action<float> OpponentIntoxicationChanged;

        // Properties
        public float PlayerIntoxication => playerIntoxication;
        public float OpponentIntoxication => opponentIntoxication;

        public IntoxicationSystem()
        {
            Reset();
        }

        /// <summary>
        /// Reset intoxication levels to zero
        /// </summary>
        public void Reset()
        {
            SetPlayerIntoxication(0f);
            SetOpponentIntoxication(0f);
        }

        /// <summary>
        /// Update intoxication decay over time
        /// </summary>
        public void Update(GameTime gameTime)
        {
            float deltaTime = (float)gameTime.ElapsedGameTime.TotalSeconds;
            
            // Apply decay to both players
            if (playerIntoxication > 0f)
            {
                float newPlayerIntox = Math.Max(0f, playerIntoxication - GameConfig.IntoxicationDecay * deltaTime);
                SetPlayerIntoxication(newPlayerIntox);
            }

            if (opponentIntoxication > 0f)
            {
                float newOpponentIntox = Math.Max(0f, opponentIntoxication - GameConfig.IntoxicationDecay * deltaTime);
                SetOpponentIntoxication(newOpponentIntox);
            }
        }

        /// <summary>
        /// Add intoxication to a player when they get hit
        /// </summary>
        public void AddIntoxication(bool isPlayer, float amount = GameConfig.IntoxicationPerCup)
        {
            if (isPlayer)
            {
                float newIntox = Math.Min(GameConfig.MaxIntoxication, playerIntoxication + amount);
                SetPlayerIntoxication(newIntox);
            }
            else
            {
                float newIntox = Math.Min(GameConfig.MaxIntoxication, opponentIntoxication + amount);
                SetOpponentIntoxication(newIntox);
            }
        }

        /// <summary>
        /// Set player intoxication level directly
        /// </summary>
        public void SetPlayerIntoxication(float level)
        {
            float oldLevel = playerIntoxication;
            playerIntoxication = Math.Clamp(level, 0f, GameConfig.MaxIntoxication);
            
            if (Math.Abs(oldLevel - playerIntoxication) > 0.001f)
            {
                PlayerIntoxicationChanged?.Invoke(playerIntoxication);
            }
        }

        /// <summary>
        /// Set opponent intoxication level directly
        /// </summary>
        public void SetOpponentIntoxication(float level)
        {
            float oldLevel = opponentIntoxication;
            opponentIntoxication = Math.Clamp(level, 0f, GameConfig.MaxIntoxication);
            
            if (Math.Abs(oldLevel - opponentIntoxication) > 0.001f)
            {
                OpponentIntoxicationChanged?.Invoke(opponentIntoxication);
            }
        }

        /// <summary>
        /// Get the line speed multiplier for aiming based on intoxication
        /// </summary>
        public float GetLineSpeedMultiplier(bool isPlayer)
        {
            float intoxication = isPlayer ? playerIntoxication : opponentIntoxication;
            return 1f + intoxication * GameConfig.MaxLineSpeedMultiplier;
        }

        /// <summary>
        /// Get the current line speed for aiming
        /// </summary>
        public float GetLineSpeed(bool isPlayer)
        {
            return GameConfig.BaseLineSpeed * GetLineSpeedMultiplier(isPlayer);
        }

        /// <summary>
        /// Get AI accuracy modifier based on intoxication
        /// </summary>
        public float GetAIAccuracyModifier(bool isPlayer)
        {
            float intoxication = isPlayer ? playerIntoxication : opponentIntoxication;
            // Higher intoxication = worse accuracy (higher inaccuracy)
            return intoxication * 20f; // Scale factor for inaccuracy
        }

        /// <summary>
        /// Check if a player is heavily intoxicated
        /// </summary>
        public bool IsHeavilyIntoxicated(bool isPlayer)
        {
            float intoxication = isPlayer ? playerIntoxication : opponentIntoxication;
            return intoxication >= 0.7f; // 70% or higher
        }

        /// <summary>
        /// Check if a player is moderately intoxicated
        /// </summary>
        public bool IsModeratelyIntoxicated(bool isPlayer)
        {
            float intoxication = isPlayer ? playerIntoxication : opponentIntoxication;
            return intoxication >= 0.4f && intoxication < 0.7f; // 40-70%
        }

        /// <summary>
        /// Check if a player is lightly intoxicated
        /// </summary>
        public bool IsLightlyIntoxicated(bool isPlayer)
        {
            float intoxication = isPlayer ? playerIntoxication : opponentIntoxication;
            return intoxication > 0f && intoxication < 0.4f; // 0-40%
        }

        /// <summary>
        /// Get intoxication level as a percentage (0-100)
        /// </summary>
        public float GetIntoxicationPercentage(bool isPlayer)
        {
            float intoxication = isPlayer ? playerIntoxication : opponentIntoxication;
            return intoxication * 100f;
        }

        /// <summary>
        /// Get intoxication level description
        /// </summary>
        public string GetIntoxicationDescription(bool isPlayer)
        {
            if (IsHeavilyIntoxicated(isPlayer))
                return "Heavily Intoxicated";
            else if (IsModeratelyIntoxicated(isPlayer))
                return "Moderately Intoxicated";
            else if (IsLightlyIntoxicated(isPlayer))
                return "Lightly Intoxicated";
            else
                return "Sober";
        }

        /// <summary>
        /// Apply special NPC intoxication resistance (for certain NPCs)
        /// </summary>
        public void ApplyIntoxicationResistance(bool isPlayer, float resistanceMultiplier)
        {
            if (isPlayer)
            {
                SetPlayerIntoxication(playerIntoxication * resistanceMultiplier);
            }
            else
            {
                SetOpponentIntoxication(opponentIntoxication * resistanceMultiplier);
            }
        }

        /// <summary>
        /// Get the visual effect intensity for intoxication (for screen effects)
        /// </summary>
        public float GetVisualEffectIntensity(bool isPlayer)
        {
            float intoxication = isPlayer ? playerIntoxication : opponentIntoxication;
            // Quadratic curve for more dramatic effect at higher levels
            return intoxication * intoxication;
        }

        /// <summary>
        /// Simulate rapid recovery (for certain NPCs with special abilities)
        /// </summary>
        public void ApplyRapidRecovery(bool isPlayer, float recoveryMultiplier)
        {
            float recoveryAmount = GameConfig.IntoxicationDecay * recoveryMultiplier;
            
            if (isPlayer)
            {
                float newIntox = Math.Max(0f, playerIntoxication - recoveryAmount);
                SetPlayerIntoxication(newIntox);
            }
            else
            {
                float newIntox = Math.Max(0f, opponentIntoxication - recoveryAmount);
                SetOpponentIntoxication(newIntox);
            }
        }

        /// <summary>
        /// Get debug information about current intoxication state
        /// </summary>
        public string GetDebugInfo()
        {
            return $"Player: {playerIntoxication:F2} ({GetIntoxicationDescription(true)}), " +
                   $"Opponent: {opponentIntoxication:F2} ({GetIntoxicationDescription(false)})";
        }
    }
}
