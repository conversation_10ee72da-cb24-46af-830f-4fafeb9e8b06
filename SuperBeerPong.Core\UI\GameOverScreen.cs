using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using SuperBeerPong.Core.Core;
using SuperBeerPong.Core.Core.Interfaces;
using System;

namespace SuperBeerPong.Core.UI
{
    /// <summary>
    /// Game over screen implementation
    /// </summary>
    public class GameOverScreen : IMenuScreen
    {
        private IInputHandler inputHandler;
        private GameResult gameResult;
        private bool isActive;
        private string message;
        private string instruction;

        // Events
        public event Action<MenuState> NavigationRequested;
        public event Action<object> ActionCompleted;

        // Properties
        public MenuState State => MenuState.GameOver;
        public bool IsActive { get; set; }

        public GameOverScreen(IInputHandler inputHandler)
        {
            this.inputHandler = inputHandler;
        }

        /// <summary>
        /// Initialize the game over screen
        /// </summary>
        public void Initialize()
        {
            isActive = false;
            message = "";
            instruction = "";
        }

        /// <summary>
        /// Set the game result to display
        /// </summary>
        public void SetGameResult(GameResult result)
        {
            gameResult = result;
            UpdateDisplayText();
        }

        /// <summary>
        /// Update game over screen logic
        /// </summary>
        public void Update(GameTime gameTime)
        {
            if (!IsActive) return;
            // Game over screen is mostly static
        }

        /// <summary>
        /// Handle input for game over screen
        /// </summary>
        public void HandleInput(GameTime gameTime)
        {
            if (!IsActive) return;

            // Handle continue/retry input
            if (inputHandler.IsMenuSelectTriggered() || inputHandler.IsKeyPressed(Microsoft.Xna.Framework.Input.Keys.Enter))
            {
                ActionCompleted?.Invoke("Continue");
            }

            // Handle back to menu
            if (inputHandler.IsMenuBackTriggered())
            {
                ActionCompleted?.Invoke("BackToMenu");
            }
        }

        /// <summary>
        /// Render the game over screen
        /// </summary>
        public void Draw(SpriteBatch spriteBatch, SpriteFont font)
        {
            if (!IsActive) return;

            var viewport = spriteBatch.GraphicsDevice.Viewport;

            // Draw semi-transparent overlay
            var overlayRect = new Rectangle(0, 0, viewport.Width, viewport.Height);
            // Note: You'll need a 1x1 white pixel texture for this
            // spriteBatch.Draw(whitePixelTexture, overlayRect, Color.Black * 0.7f);

            // Draw main message
            Vector2 messageSize = font.MeasureString(message);
            Vector2 messagePos = new Vector2(
                (viewport.Width - messageSize.X) / 2,
                viewport.Height / 2 - 50
            );

            Color messageColor = gameResult.PlayerWon ? 
                GameConfig.GameOverWinColor : GameConfig.GameOverLoseColor;

            spriteBatch.DrawString(font, message, messagePos, messageColor);

            // Draw game stats
            DrawGameStats(spriteBatch, font, viewport);

            // Draw instruction
            Vector2 instructionSize = font.MeasureString(instruction);
            Vector2 instructionPos = new Vector2(
                (viewport.Width - instructionSize.X) / 2,
                viewport.Height - 100
            );
            spriteBatch.DrawString(font, instruction, instructionPos, Color.White);
        }

        /// <summary>
        /// Called when this screen becomes active
        /// </summary>
        public void OnEnter()
        {
            IsActive = true;
        }

        /// <summary>
        /// Called when this screen becomes inactive
        /// </summary>
        public void OnExit()
        {
            IsActive = false;
        }

        /// <summary>
        /// Update the display text based on game result
        /// </summary>
        private void UpdateDisplayText()
        {
            if (gameResult.PlayerWon)
            {
                message = "YOU WIN!";
                instruction = "Press ENTER for next opponent, ESC for main menu";
            }
            else
            {
                message = "YOU LOSE!";
                instruction = "Press ENTER to retry, ESC for main menu";
            }

            // Customize message based on game phase
            switch (gameResult.FinalPhase)
            {
                case GamePhase.Rebuttal:
                    if (gameResult.PlayerWon)
                        message = "REBUTTAL SUCCESS!";
                    else
                        message = "REBUTTAL FAILED!";
                    break;

                case GamePhase.Overtime:
                    if (gameResult.PlayerWon)
                        message = "OVERTIME VICTORY!";
                    else
                        message = "OVERTIME DEFEAT!";
                    break;
            }
        }

        /// <summary>
        /// Draw game statistics
        /// </summary>
        private void DrawGameStats(SpriteBatch spriteBatch, SpriteFont font, Viewport viewport)
        {
            var stats = new[]
            {
                $"Player Cups Remaining: {gameResult.PlayerCupsRemaining}",
                $"Opponent Cups Remaining: {gameResult.OpponentCupsRemaining}",
                $"Final Phase: {gameResult.FinalPhase}",
                $"Total Shots: {gameResult.TotalShots}"
            };

            Vector2 statsStart = new Vector2(viewport.Width / 2, viewport.Height / 2 + 20);

            for (int i = 0; i < stats.Length; i++)
            {
                Vector2 statSize = font.MeasureString(stats[i]);
                Vector2 statPos = new Vector2(
                    statsStart.X - statSize.X / 2,
                    statsStart.Y + i * 25
                );
                spriteBatch.DrawString(font, stats[i], statPos, Color.Gray);
            }
        }

        /// <summary>
        /// Set custom message and instruction
        /// </summary>
        public void SetCustomMessage(string message, string instruction)
        {
            this.message = message;
            this.instruction = instruction;
        }

        /// <summary>
        /// Check if player won the game
        /// </summary>
        public bool DidPlayerWin()
        {
            return gameResult.PlayerWon;
        }

        /// <summary>
        /// Get the game result
        /// </summary>
        public GameResult GetGameResult()
        {
            return gameResult;
        }
    }
}
