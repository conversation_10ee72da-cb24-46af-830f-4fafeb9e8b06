using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using SuperBeerPong.Core.Core;
using SuperBeerPong.Core.Core.Interfaces;
using SuperBeerPong.Core.Systems;
using System;
using System.Collections.Generic;

namespace SuperBeerPong.Core.GameModes
{
    /// <summary>
    /// Base class for all game modes with shared functionality
    /// </summary>
    public abstract class BaseGameMode : IGameMode
    {
        // Core systems
        protected PhysicsSystem physicsSystem;
        protected IntoxicationSystem intoxicationSystem;
        protected AimingSystem aimingSystem;
        protected RebuttalSystem rebuttalSystem;
        protected IInputHandler inputHandler;

        // Graphics resources
        protected GraphicsDevice graphicsDevice;
        protected SpriteBatch spriteBatch;
        protected SpriteFont font;
        protected Texture2D pixelTexture;

        // Game entities
        protected List<Cup> playerCups;
        protected List<Cup> opponentCups;
        protected Rectangle tableRect;

        // Game state
        protected bool isPlayerTurn;
        protected bool isActive;
        protected bool isPaused;
        protected int totalShots;

        // Events
        public event Action<GameResult> GameCompleted;
        public event Action<GameModeType> ModeChangeRequested;

        // Properties
        public abstract GameModeType ModeType { get; }
        public Core.GamePhase CurrentPhase => rebuttalSystem.CurrentPhase;
        public bool IsActive => isActive;

        protected BaseGameMode(IInputHandler inputHandler)
        {
            this.inputHandler = inputHandler;
            
            // Initialize systems
            physicsSystem = new PhysicsSystem();
            intoxicationSystem = new IntoxicationSystem();
            aimingSystem = new AimingSystem();
            rebuttalSystem = new RebuttalSystem();

            // Initialize collections
            playerCups = new List<Cup>();
            opponentCups = new List<Cup>();

            // Setup event handlers
            SetupEventHandlers();
        }

        /// <summary>
        /// Initialize the game mode with graphics resources
        /// </summary>
        public virtual void Initialize(GraphicsDevice graphicsDevice, SpriteBatch spriteBatch)
        {
            this.graphicsDevice = graphicsDevice;
            this.spriteBatch = spriteBatch;

            // Calculate table rectangle
            tableRect = GameConfig.GetTableRectangle(
                graphicsDevice.Viewport.Width, 
                graphicsDevice.Viewport.Height
            );

            // Initialize systems
            physicsSystem.Initialize(tableRect);
            aimingSystem.Initialize(graphicsDevice.Viewport.Width, graphicsDevice.Viewport.Height);
        }

        /// <summary>
        /// Set graphics resources
        /// </summary>
        public virtual void SetGraphicsResources(SpriteFont font, Texture2D pixelTexture)
        {
            this.font = font;
            this.pixelTexture = pixelTexture;
        }

        /// <summary>
        /// Start a new game
        /// </summary>
        public virtual void StartGame()
        {
            isActive = true;
            isPaused = false;
            isPlayerTurn = true;
            totalShots = 0;

            // Reset systems
            physicsSystem.ClearAllBalls();
            intoxicationSystem.Reset();
            aimingSystem.Reset();
            rebuttalSystem.Initialize(GameConfig.MainGameCupsPerSide, GameConfig.MainGameCupsPerSide);

            // Setup cup formations
            SetupCupFormations();

            // Start player's turn
            StartPlayerTurn();
        }

        /// <summary>
        /// Update the game mode
        /// </summary>
        public virtual void Update(GameTime gameTime)
        {
            if (!isActive || isPaused) return;

            // Update systems
            physicsSystem.Update(gameTime, playerCups, opponentCups);
            intoxicationSystem.Update(gameTime);
            
            // Update aiming system with current intoxication effects
            float lineSpeed = intoxicationSystem.GetLineSpeed(isPlayerTurn);
            aimingSystem.Update(gameTime, lineSpeed);

            // Handle turn logic
            UpdateTurnLogic(gameTime);
        }

        /// <summary>
        /// Handle input for the game mode
        /// </summary>
        public virtual void HandleInput(GameTime gameTime)
        {
            if (!isActive || isPaused) return;

            // Handle aiming input for player turns
            if (isPlayerTurn && aimingSystem.IsActive)
            {
                if (aimingSystem.HandleInput(inputHandler))
                {
                    // Player took a shot
                    totalShots++;
                }
            }

            // Handle pause input
            if (inputHandler.IsActionTriggered(InputAction.Pause))
            {
                SetPaused(!isPaused);
            }

            // Handle exit input
            if (inputHandler.IsExitRequested())
            {
                EndGameMode();
            }
        }

        /// <summary>
        /// Draw the game mode
        /// </summary>
        public virtual void Draw(GameTime gameTime)
        {
            if (!isActive) return;

            DrawTable();
            DrawCups();
            DrawBalls();
            DrawAiming();
            DrawHUD();

            if (isPaused)
            {
                DrawPauseOverlay();
            }
        }

        /// <summary>
        /// End the current game and return results
        /// </summary>
        public virtual GameResult EndGame(bool playerWon)
        {
            isActive = false;

            GameResult result = new GameResult(
                playerWon,
                CountActiveCups(playerCups),
                CountActiveCups(opponentCups),
                rebuttalSystem.CurrentPhase
            )
            {
                PlayerIntoxication = intoxicationSystem.PlayerIntoxication,
                OpponentIntoxication = intoxicationSystem.OpponentIntoxication,
                TotalShots = totalShots,
                WasRebuttal = rebuttalSystem.IsRebuttalActive
            };

            GameCompleted?.Invoke(result);
            return result;
        }

        /// <summary>
        /// Reset the game mode
        /// </summary>
        public virtual void Reset()
        {
            isActive = false;
            isPaused = false;
            isPlayerTurn = true;
            totalShots = 0;

            physicsSystem.ClearAllBalls();
            intoxicationSystem.Reset();
            aimingSystem.Reset();
            rebuttalSystem.Reset();

            playerCups.Clear();
            opponentCups.Clear();
        }

        /// <summary>
        /// Pause or unpause the game
        /// </summary>
        public virtual void SetPaused(bool paused)
        {
            isPaused = paused;
        }

        /// <summary>
        /// Get current game state
        /// </summary>
        public virtual GameResult GetCurrentState()
        {
            return new GameResult(
                false, // Game not finished yet
                CountActiveCups(playerCups),
                CountActiveCups(opponentCups),
                rebuttalSystem.CurrentPhase
            )
            {
                PlayerIntoxication = intoxicationSystem.PlayerIntoxication,
                OpponentIntoxication = intoxicationSystem.OpponentIntoxication,
                TotalShots = totalShots,
                WasRebuttal = rebuttalSystem.IsRebuttalActive
            };
        }

        /// <summary>
        /// Setup cup formations for the game
        /// </summary>
        protected virtual void SetupCupFormations()
        {
            playerCups.Clear();
            opponentCups.Clear();

            if (rebuttalSystem.CurrentPhase == GamePhase.Overtime)
            {
                SetupOvertimeCups();
            }
            else
            {
                SetupMainGameCups();
            }
        }

        /// <summary>
        /// Setup main game cup formations (10 cups each)
        /// </summary>
        protected virtual void SetupMainGameCups()
        {
            // Player's cups at bottom (4-3-2-1 triangle)
            SetupTriangleFormation(playerCups, 
                new Vector2(tableRect.Center.X, tableRect.Bottom - GameConfig.TableMarginBottom),
                GameConfig.TriangleRowCounts, false);

            // Opponent's cups at top (4-3-2-1 triangle)
            SetupTriangleFormation(opponentCups,
                new Vector2(tableRect.Center.X, tableRect.Top + GameConfig.TableMarginTop),
                GameConfig.TriangleRowCounts, true);
        }

        /// <summary>
        /// Setup overtime cup formations (3 cups each)
        /// </summary>
        protected virtual void SetupOvertimeCups()
        {
            // Player's cups at bottom (2-1 triangle)
            SetupTriangleFormation(playerCups,
                new Vector2(tableRect.Center.X, tableRect.Bottom - 80),
                GameConfig.OvertimeRowCounts, false);

            // Opponent's cups at top (2-1 triangle)
            SetupTriangleFormation(opponentCups,
                new Vector2(tableRect.Center.X, tableRect.Top + 80),
                GameConfig.OvertimeRowCounts, true);
        }

        /// <summary>
        /// Setup triangle formation of cups
        /// </summary>
        protected void SetupTriangleFormation(List<Cup> cups, Vector2 baseCenter, int[] rowCounts, bool pointUp)
        {
            cups.Clear();
            float currentY = baseCenter.Y;
            int cupId = 0;

            foreach (int cupsInRow in rowCounts)
            {
                float rowWidth = (cupsInRow - 1) * GameConfig.CupSpacing;
                float startX = baseCenter.X - rowWidth / 2f;

                for (int i = 0; i < cupsInRow; i++)
                {
                    Vector2 cupPos = new Vector2(startX + i * GameConfig.CupSpacing, currentY);
                    cups.Add(new Cup(cupPos, cupId++));
                }

                // Move to next row
                float yOffset = GameConfig.CupSpacing * GameConfig.HexPackingRatio;
                currentY += pointUp ? yOffset : -yOffset;
            }
        }

        /// <summary>
        /// Start player's turn
        /// </summary>
        protected virtual void StartPlayerTurn()
        {
            isPlayerTurn = true;
            aimingSystem.StartAiming();
        }

        /// <summary>
        /// Start opponent's turn (to be overridden by specific game modes)
        /// </summary>
        protected abstract void StartOpponentTurn();

        /// <summary>
        /// Update turn logic
        /// </summary>
        protected virtual void UpdateTurnLogic(GameTime gameTime)
        {
            // Check if all balls have finished
            if (!physicsSystem.HasActiveBalls && aimingSystem.IsShotPending)
            {
                // Shot completed, handle turn end
                HandleShotCompleted();
            }
        }

        /// <summary>
        /// Handle shot completion
        /// </summary>
        protected virtual void HandleShotCompleted()
        {
            aimingSystem.Reset();

            // Check if this was a rebuttal miss
            if (rebuttalSystem.IsRebuttalActive)
            {
                rebuttalSystem.HandleRebuttalMiss();
            }
            else
            {
                // Normal turn end
                EndTurn();
            }
        }

        /// <summary>
        /// End current turn
        /// </summary>
        protected virtual void EndTurn()
        {
            if (rebuttalSystem.AllowsContinuousShooting())
            {
                // During rebuttal, same player continues
                if (isPlayerTurn)
                    StartPlayerTurn();
                else
                    StartOpponentTurn();
            }
            else
            {
                // Normal turn switching
                isPlayerTurn = !isPlayerTurn;
                if (isPlayerTurn)
                    StartPlayerTurn();
                else
                    StartOpponentTurn();
            }
        }

        /// <summary>
        /// Setup event handlers for systems
        /// </summary>
        protected virtual void SetupEventHandlers()
        {
            // Physics system events
            physicsSystem.BallHitCup += OnBallHitCup;

            // Aiming system events
            aimingSystem.ShotTaken += OnShotTaken;

            // Rebuttal system events
            rebuttalSystem.GameEnded += OnGameEnded;
            rebuttalSystem.OvertimeStarted += OnOvertimeStarted;
        }

        /// <summary>
        /// Handle ball hitting a cup
        /// </summary>
        protected virtual void OnBallHitCup(Ball ball, Cup cup)
        {
            // Remove the cup
            RemoveCup(cup);

            // Add intoxication to the player who got hit
            bool playerGotHit = playerCups.Contains(cup);
            intoxicationSystem.AddIntoxication(playerGotHit);

            // Check game state
            rebuttalSystem.CheckGameStateAfterCupHit(playerCups, opponentCups);
        }

        /// <summary>
        /// Handle shot being taken
        /// </summary>
        protected virtual void OnShotTaken(Vector2 targetPosition)
        {
            // Create ball from appropriate position
            Vector2 startPos = isPlayerTurn ? 
                new Vector2(tableRect.Center.X, tableRect.Bottom + 50) :
                new Vector2(tableRect.Center.X, tableRect.Top - 50);

            physicsSystem.AddBall(startPos, targetPosition, isPlayerTurn);
        }

        /// <summary>
        /// Handle game ending
        /// </summary>
        protected virtual void OnGameEnded(bool playerWon)
        {
            EndGame(playerWon);
        }

        /// <summary>
        /// Handle overtime starting
        /// </summary>
        protected virtual void OnOvertimeStarted()
        {
            SetupOvertimeCups();
            StartPlayerTurn(); // Player starts overtime
        }

        /// <summary>
        /// Remove a cup from the appropriate list
        /// </summary>
        protected void RemoveCup(Cup cup)
        {
            for (int i = 0; i < playerCups.Count; i++)
            {
                if (playerCups[i].ID == cup.ID)
                {
                    var updatedCup = playerCups[i];
                    updatedCup.Hit();
                    playerCups[i] = updatedCup;
                    return;
                }
            }

            for (int i = 0; i < opponentCups.Count; i++)
            {
                if (opponentCups[i].ID == cup.ID)
                {
                    var updatedCup = opponentCups[i];
                    updatedCup.Hit();
                    opponentCups[i] = updatedCup;
                    return;
                }
            }
        }

        /// <summary>
        /// Count active cups in a list
        /// </summary>
        protected int CountActiveCups(List<Cup> cups)
        {
            int count = 0;
            foreach (Cup cup in cups)
            {
                if (cup.IsActive) count++;
            }
            return count;
        }

        /// <summary>
        /// End the game mode and return to menu
        /// </summary>
        protected virtual void EndGameMode()
        {
            Reset();
            ModeChangeRequested?.Invoke(GameModeType.StoryMode); // Default to story mode
        }

        // Abstract drawing methods to be implemented by derived classes
        protected abstract void DrawTable();
        protected abstract void DrawCups();
        protected abstract void DrawBalls();
        protected abstract void DrawAiming();
        protected abstract void DrawHUD();
        protected abstract void DrawPauseOverlay();
    }
}
