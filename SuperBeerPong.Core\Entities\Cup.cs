using Microsoft.Xna.Framework;
using SuperBeerPong.Core.Core;
using System;

namespace SuperBeerPong.Core
{
    /// <summary>
    /// Represents a beer pong cup on the table
    /// </summary>
    public struct Cup
    {
        public Vector2 Pos;
        public bool IsActive; // Whether the cup is still in play
        public float AnimationTime; // For hit animations
        public bool IsBeingHit; // Animation state
        public int ID; // Unique identifier for tracking

        public Cup(Vector2 position, int id = -1)
        {
            Pos = position;
            IsActive = true;
            AnimationTime = 0f;
            IsBeingHit = false;
            ID = id;
        }

        /// <summary>
        /// Update cup animations
        /// </summary>
        public void Update(float deltaTime)
        {
            if (IsBeingHit)
            {
                AnimationTime += deltaTime;
                
                // Hit animation lasts 0.5 seconds
                if (AnimationTime >= 0.5f)
                {
                    IsBeingHit = false;
                    IsActive = false; // Cup is removed after animation
                    AnimationTime = 0f;
                }
            }
        }

        /// <summary>
        /// Mark this cup as hit and start removal animation
        /// </summary>
        public void Hit()
        {
            if (!IsActive) return;
            
            IsBeingHit = true;
            AnimationTime = 0f;
        }

        /// <summary>
        /// Remove the cup immediately without animation
        /// </summary>
        public void Remove()
        {
            IsActive = false;
            IsBeingHit = false;
            AnimationTime = 0f;
        }

        /// <summary>
        /// Get the visual scale for rendering (for hit animation)
        /// </summary>
        public float GetScale()
        {
            if (!IsBeingHit) return 1.0f;
            
            // Scale down during hit animation
            float progress = AnimationTime / 0.5f;
            return 1.0f - (progress * 0.3f); // Scale down by 30% during animation
        }

        /// <summary>
        /// Get the visual alpha for rendering (for hit animation)
        /// </summary>
        public float GetAlpha()
        {
            if (!IsBeingHit) return 1.0f;
            
            // Fade out during hit animation
            float progress = AnimationTime / 0.5f;
            return 1.0f - progress;
        }

        /// <summary>
        /// Get the render position (with animation offset)
        /// </summary>
        public Vector2 GetRenderPosition()
        {
            if (!IsBeingHit) return Pos;
            
            // Slight upward movement during hit animation
            float progress = AnimationTime / 0.5f;
            float yOffset = -10f * progress;
            return new Vector2(Pos.X, Pos.Y + yOffset);
        }

        /// <summary>
        /// Check if a point is within the cup's collision area
        /// </summary>
        public bool ContainsPoint(Vector2 point)
        {
            if (!IsActive) return false;
            
            float distance = Vector2.Distance(Pos, point);
            return distance <= GameConfig.CupRadius;
        }

        /// <summary>
        /// Get the cup's bounding rectangle
        /// </summary>
        public Rectangle GetBounds()
        {
            return new Rectangle(
                (int)(Pos.X - GameConfig.CupRadius),
                (int)(Pos.Y - GameConfig.CupRadius),
                (int)GameConfig.CupDiameter,
                (int)GameConfig.CupDiameter
            );
        }

        /// <summary>
        /// Check collision with another cup (for formation validation)
        /// </summary>
        public bool CollidesWith(Cup other)
        {
            if (!IsActive || !other.IsActive) return false;
            
            float distance = Vector2.Distance(Pos, other.Pos);
            return distance < GameConfig.CupDiameter; // Cups overlap if closer than diameter
        }

        /// <summary>
        /// Get distance to another cup
        /// </summary>
        public float DistanceTo(Cup other)
        {
            return Vector2.Distance(Pos, other.Pos);
        }

        /// <summary>
        /// Check if this cup is adjacent to another cup (touching brims)
        /// </summary>
        public bool IsAdjacentTo(Cup other)
        {
            if (!IsActive || !other.IsActive) return false;
            
            float distance = Vector2.Distance(Pos, other.Pos);
            float touchingDistance = GameConfig.CupSpacing;
            
            // Allow small tolerance for floating point precision
            return Math.Abs(distance - touchingDistance) < 2f;
        }

        /// <summary>
        /// Create a copy of this cup at a new position
        /// </summary>
        public Cup MoveTo(Vector2 newPosition)
        {
            return new Cup(newPosition, ID)
            {
                IsActive = this.IsActive,
                AnimationTime = this.AnimationTime,
                IsBeingHit = this.IsBeingHit
            };
        }

        /// <summary>
        /// Get the cup's center point for calculations
        /// </summary>
        public Vector2 Center => Pos;

        /// <summary>
        /// Check if the cup is visible (active and not fully faded)
        /// </summary>
        public bool IsVisible => IsActive || (IsBeingHit && GetAlpha() > 0.1f);

        /// <summary>
        /// Get debug information about the cup
        /// </summary>
        public override string ToString()
        {
            return $"Cup {ID}: Pos({Pos.X:F1}, {Pos.Y:F1}), Active: {IsActive}, Hit: {IsBeingHit}";
        }
    }
}
