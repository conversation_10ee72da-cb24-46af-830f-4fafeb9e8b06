using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Input;
using SuperBeerPong.Core.Core;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SuperBeerPong.Core.Core.Interfaces
{
    /// <summary>
    /// Interface for centralized input handling
    /// </summary>
    public interface IInputHandler
    {
        /// <summary>
        /// Current keyboard state
        /// </summary>
        KeyboardState CurrentKeyboardState { get; }

        /// <summary>
        /// Previous keyboard state for detecting key presses
        /// </summary>
        KeyboardState PreviousKeyboardState { get; }

        /// <summary>
        /// Current mouse state
        /// </summary>
        MouseState CurrentMouseState { get; }

        /// <summary>
        /// Previous mouse state for detecting clicks
        /// </summary>
        MouseState PreviousMouseState { get; }

        /// <summary>
        /// Update input states
        /// </summary>
        void Update(GameTime gameTime);

        /// <summary>
        /// Check if a key was just pressed (down this frame, up last frame)
        /// </summary>
        bool IsKeyPressed(Keys key);

        /// <summary>
        /// Check if a key is currently held down
        /// </summary>
        bool IsKeyDown(Keys key);

        /// <summary>
        /// Check if a key was just released
        /// </summary>
        bool IsKeyReleased(Keys key);

        /// <summary>
        /// Check if left mouse button was just clicked
        /// </summary>
        bool IsLeftMousePressed();

        /// <summary>
        /// Check if left mouse button is currently held
        /// </summary>
        bool IsLeftMouseDown();

        /// <summary>
        /// Check if right mouse button was just clicked
        /// </summary>
        bool IsRightMousePressed();

        /// <summary>
        /// Get current mouse position
        /// </summary>
        Vector2 GetMousePosition();

        /// <summary>
        /// Get mouse movement delta since last frame
        /// </summary>
        Vector2 GetMouseDelta();

        /// <summary>
        /// Check if a specific input action was triggered
        /// </summary>
        bool IsActionTriggered(InputAction action);

        /// <summary>
        /// Check if a specific input action is currently active
        /// </summary>
        bool IsActionActive(InputAction action);

        /// <summary>
        /// Map a key to an input action
        /// </summary>
        void MapKeyToAction(Keys key, InputAction action);

        /// <summary>
        /// Get all currently pressed keys
        /// </summary>
        Keys[] GetPressedKeys();

        /// <summary>
        /// Check if any key was pressed this frame
        /// </summary>
        bool AnyKeyPressed();

        /// <summary>
        /// Check if exit was requested (escape key or exit action)
        /// </summary>
        bool IsExitRequested();

        /// <summary>
        /// Check if menu select was triggered
        /// </summary>
        bool IsMenuSelectTriggered();

        /// <summary>
        /// Check if menu back was triggered
        /// </summary>
        bool IsMenuBackTriggered();

        /// <summary>
        /// Get menu navigation direction (-1 for up, 1 for down, 0 for none)
        /// </summary>
        int GetMenuNavigation();

        /// <summary>
        /// Event fired when a key is pressed
        /// </summary>
        event System.Action<Keys> KeyPressed;

        /// <summary>
        /// Event fired when a mouse button is clicked
        /// </summary>
        event System.Action<Vector2> MouseClicked;

        /// <summary>
        /// Event fired when an input action is triggered
        /// </summary>
        event System.Action<InputAction> ActionTriggered;
    }

    /// <summary>
    /// Input configuration for customizable controls
    /// </summary>
    public struct InputMapping
    {
        public InputAction Action { get; set; }
        public Keys PrimaryKey { get; set; }
        public Keys SecondaryKey { get; set; }
        public bool RequireShift { get; set; }
        public bool RequireCtrl { get; set; }
        public bool RequireAlt { get; set; }

        public InputMapping(InputAction action, Keys primaryKey, Keys secondaryKey = Keys.None)
        {
            Action = action;
            PrimaryKey = primaryKey;
            SecondaryKey = secondaryKey;
            RequireShift = false;
            RequireCtrl = false;
            RequireAlt = false;
        }
    }

    /// <summary>
    /// Default input mappings for the game
    /// </summary>
    public static class DefaultInputMappings
    {
        public static readonly InputMapping[] Mappings = new[]
        {
            new InputMapping(InputAction.MenuUp, Keys.Up, Keys.W),
            new InputMapping(InputAction.MenuDown, Keys.Down, Keys.S),
            new InputMapping(InputAction.MenuSelect, Keys.Enter, Keys.Space),
            new InputMapping(InputAction.MenuBack, Keys.Escape, Keys.Back),
            new InputMapping(InputAction.Shoot, Keys.Space, Keys.Enter),
            new InputMapping(InputAction.Pause, Keys.P, Keys.Escape),
            new InputMapping(InputAction.Exit, Keys.Escape, Keys.Q)
        };

        /// <summary>
        /// Get default mapping for an action
        /// </summary>
        public static InputMapping GetMapping(InputAction action)
        {
            return Mappings.FirstOrDefault(m => m.Action == action);
        }

        /// <summary>
        /// Get all mappings as a dictionary
        /// </summary>
        public static Dictionary<InputAction, InputMapping> GetMappingDictionary()
        {
            return Mappings.ToDictionary(m => m.Action, m => m);
        }
    }
}
