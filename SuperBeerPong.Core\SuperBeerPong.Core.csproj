﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Platforms>AnyCPU</Platforms>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="MonoGame.Framework.DesktopGL" Version="3.8.*">
      <PrivateAssets>All</PrivateAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <None Update="Content\ball.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Content\Sprites\ball.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Content\Sprites\cup.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Content\Sprites\profile-tony.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Content\Sprites\table.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Graphics\" />
  </ItemGroup>
</Project>