using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using SuperBeerPong.Core.Core;

namespace SuperBeerPong.Core.Core.Interfaces
{
    /// <summary>
    /// Interface for menu system management
    /// </summary>
    public interface IMenuSystem
    {
        /// <summary>
        /// Current menu state
        /// </summary>
        MenuState CurrentState { get; }

        /// <summary>
        /// Whether the menu system is currently active
        /// </summary>
        bool IsActive { get; }

        /// <summary>
        /// Initialize the menu system
        /// </summary>
        void Initialize(GraphicsDevice graphicsDevice, SpriteBatch spriteBatch);

        /// <summary>
        /// Update menu logic and handle input
        /// </summary>
        void Update(GameTime gameTime);

        /// <summary>
        /// Render the current menu
        /// </summary>
        void Draw(GameTime gameTime);

        /// <summary>
        /// Navigate to a specific menu state
        /// </summary>
        void NavigateTo(MenuState state);

        /// <summary>
        /// Go back to the previous menu
        /// </summary>
        void GoBack();

        /// <summary>
        /// Show the menu system
        /// </summary>
        void Show();

        /// <summary>
        /// Hide the menu system
        /// </summary>
        void Hide();

        /// <summary>
        /// Reset menu to initial state
        /// </summary>
        void Reset();

        /// <summary>
        /// Event fired when a game mode is selected
        /// </summary>
        event System.Action<GameModeType> GameModeSelected;

        /// <summary>
        /// Event fired when the application should exit
        /// </summary>
        event System.Action ExitRequested;

        /// <summary>
        /// Event fired when settings should be opened
        /// </summary>
        event System.Action SettingsRequested;
    }

    /// <summary>
    /// Interface for individual menu screens
    /// </summary>
    public interface IMenuScreen
    {
        /// <summary>
        /// Menu state this screen represents
        /// </summary>
        MenuState State { get; }

        /// <summary>
        /// Whether this screen is currently active
        /// </summary>
        bool IsActive { get; set; }

        /// <summary>
        /// Initialize the menu screen
        /// </summary>
        void Initialize();

        /// <summary>
        /// Update the menu screen
        /// </summary>
        void Update(GameTime gameTime);

        /// <summary>
        /// Render the menu screen
        /// </summary>
        void Draw(SpriteBatch spriteBatch, SpriteFont font);

        /// <summary>
        /// Handle input for this menu screen
        /// </summary>
        void HandleInput(GameTime gameTime);

        /// <summary>
        /// Called when this screen becomes active
        /// </summary>
        void OnEnter();

        /// <summary>
        /// Called when this screen becomes inactive
        /// </summary>
        void OnExit();

        /// <summary>
        /// Event fired when this screen wants to navigate to another screen
        /// </summary>
        event System.Action<MenuState> NavigationRequested;

        /// <summary>
        /// Event fired when this screen has completed its action
        /// </summary>
        event System.Action<object> ActionCompleted;
    }

    /// <summary>
    /// Interface for UI components that can be rendered
    /// </summary>
    public interface IUIComponent
    {
        /// <summary>
        /// Position of the component
        /// </summary>
        Vector2 Position { get; set; }

        /// <summary>
        /// Size of the component
        /// </summary>
        Vector2 Size { get; set; }

        /// <summary>
        /// Whether the component is visible
        /// </summary>
        bool IsVisible { get; set; }

        /// <summary>
        /// Whether the component is enabled for interaction
        /// </summary>
        bool IsEnabled { get; set; }

        /// <summary>
        /// Update the component
        /// </summary>
        void Update(GameTime gameTime);

        /// <summary>
        /// Render the component
        /// </summary>
        void Draw(SpriteBatch spriteBatch);

        /// <summary>
        /// Handle input for the component
        /// </summary>
        void HandleInput(GameTime gameTime);
    }
}
