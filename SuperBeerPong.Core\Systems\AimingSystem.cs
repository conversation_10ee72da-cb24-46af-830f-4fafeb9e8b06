using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using SuperBeerPong.Core.Core;
using SuperBeerPong.Core.Core.Interfaces;
using System;

namespace SuperBeerPong.Core.Systems
{
    /// <summary>
    /// Manages the aiming system for player shots
    /// </summary>
    public class AimingSystem
    {
        private Rectangle aimBox;
        private Vector2 hLinePos, vLinePos;
        private float lineSpeed;
        private bool horizLocked, shotPending;
        private bool isActive;

        // Line movement direction
        private int hDirection = 1;
        private int vDirection = 1;

        // Events
        public event Action<Vector2> ShotTaken;
        public event Action AimingStarted;
        public event Action AimingReset;

        // Properties
        public bool IsHorizontalLocked => horizLocked;
        public bool IsShotPending => shotPending;
        public bool IsActive => isActive;
        public Vector2 CurrentTarget => GetTargetPosition();

        public AimingSystem()
        {
            Reset();
        }

        /// <summary>
        /// Initialize the aiming system with screen dimensions
        /// </summary>
        public void Initialize(int screenWidth, int screenHeight)
        {
            aimBox = GameConfig.GetAimBoxRectangle(screenWidth, screenHeight);
            Reset();
        }

        /// <summary>
        /// Update the aiming system
        /// </summary>
        public void Update(GameTime gameTime, float currentLineSpeed)
        {
            if (!isActive || shotPending) return;

            lineSpeed = currentLineSpeed;
            float deltaTime = (float)gameTime.ElapsedGameTime.TotalSeconds;
            float movement = lineSpeed * deltaTime;

            // Update horizontal line movement
            if (!horizLocked)
            {
                hLinePos.X += movement * hDirection;
                
                // Bounce off edges
                if (hLinePos.X <= aimBox.Left)
                {
                    hLinePos.X = aimBox.Left;
                    hDirection = 1;
                }
                else if (hLinePos.X >= aimBox.Right)
                {
                    hLinePos.X = aimBox.Right;
                    hDirection = -1;
                }
            }
            else
            {
                // Update vertical line movement when horizontal is locked
                vLinePos.Y += movement * vDirection;
                
                // Bounce off edges
                if (vLinePos.Y <= aimBox.Top)
                {
                    vLinePos.Y = aimBox.Top;
                    vDirection = 1;
                }
                else if (vLinePos.Y >= aimBox.Bottom)
                {
                    vLinePos.Y = aimBox.Bottom;
                    vDirection = -1;
                }
            }
        }

        /// <summary>
        /// Handle input for the aiming system
        /// </summary>
        public bool HandleInput(IInputHandler inputHandler)
        {
            if (!isActive || shotPending) return false;

            if (inputHandler.IsActionTriggered(InputAction.Shoot) || inputHandler.IsLeftMousePressed())
            {
                if (!horizLocked)
                {
                    // Lock horizontal position
                    LockHorizontal();
                    return false; // Don't shoot yet
                }
                else
                {
                    // Take the shot
                    TakeShot();
                    return true; // Shot taken
                }
            }

            return false;
        }

        /// <summary>
        /// Start the aiming process
        /// </summary>
        public void StartAiming()
        {
            isActive = true;
            horizLocked = false;
            shotPending = false;
            
            // Reset line positions to center
            hLinePos = new Vector2(aimBox.Center.X, aimBox.Center.Y);
            vLinePos = new Vector2(aimBox.Center.X, aimBox.Center.Y);
            
            // Reset directions
            hDirection = 1;
            vDirection = 1;

            AimingStarted?.Invoke();
        }

        /// <summary>
        /// Reset the aiming system
        /// </summary>
        public void Reset()
        {
            isActive = false;
            horizLocked = false;
            shotPending = false;
            lineSpeed = GameConfig.BaseLineSpeed;
            
            // Reset line positions
            if (aimBox.Width > 0 && aimBox.Height > 0)
            {
                hLinePos = new Vector2(aimBox.Center.X, aimBox.Center.Y);
                vLinePos = new Vector2(aimBox.Center.X, aimBox.Center.Y);
            }

            AimingReset?.Invoke();
        }

        /// <summary>
        /// Lock the horizontal position and start vertical aiming
        /// </summary>
        private void LockHorizontal()
        {
            horizLocked = true;
            vLinePos.X = hLinePos.X; // Set vertical line to same X position
        }

        /// <summary>
        /// Take the shot at current position
        /// </summary>
        private void TakeShot()
        {
            shotPending = true;
            isActive = false;
            
            Vector2 targetPosition = GetTargetPosition();
            ShotTaken?.Invoke(targetPosition);
        }

        /// <summary>
        /// Get the current target position based on line positions
        /// </summary>
        public Vector2 GetTargetPosition()
        {
            if (!horizLocked)
            {
                // If horizontal not locked, use current horizontal line position
                return new Vector2(hLinePos.X, aimBox.Center.Y);
            }
            else
            {
                // Use both horizontal and vertical positions
                return new Vector2(hLinePos.X, vLinePos.Y);
            }
        }

        /// <summary>
        /// Draw the aiming system
        /// </summary>
        public void Draw(SpriteBatch spriteBatch, Texture2D pixelTexture)
        {
            if (!isActive) return;

            // Draw aim box outline
            DrawRectangleOutline(spriteBatch, pixelTexture, aimBox, Color.White, 2);

            // Draw horizontal line (always visible)
            Rectangle hLine = new Rectangle(
                aimBox.Left,
                (int)hLinePos.Y - 1,
                aimBox.Width,
                (int)GameConfig.AimLineThickness
            );
            spriteBatch.Draw(pixelTexture, hLine, horizLocked ? Color.Red : Color.Yellow);

            // Draw vertical line (only when horizontal is locked)
            if (horizLocked)
            {
                Rectangle vLine = new Rectangle(
                    (int)vLinePos.X - 1,
                    aimBox.Top,
                    (int)GameConfig.AimLineThickness,
                    aimBox.Height
                );
                spriteBatch.Draw(pixelTexture, vLine, Color.Green);

                // Draw crosshair at intersection
                Vector2 crosshair = GetTargetPosition();
                Rectangle crosshairH = new Rectangle(
                    (int)crosshair.X - 5,
                    (int)crosshair.Y - 1,
                    10,
                    (int)GameConfig.AimLineThickness
                );
                Rectangle crosshairV = new Rectangle(
                    (int)crosshair.X - 1,
                    (int)crosshair.Y - 5,
                    (int)GameConfig.AimLineThickness,
                    10
                );
                spriteBatch.Draw(pixelTexture, crosshairH, Color.Red);
                spriteBatch.Draw(pixelTexture, crosshairV, Color.Red);
            }
        }

        /// <summary>
        /// Set the aiming box rectangle
        /// </summary>
        public void SetAimBox(Rectangle newAimBox)
        {
            aimBox = newAimBox;
            Reset();
        }

        /// <summary>
        /// Get the aiming box rectangle
        /// </summary>
        public Rectangle GetAimBox()
        {
            return aimBox;
        }

        /// <summary>
        /// Set the line speed directly
        /// </summary>
        public void SetLineSpeed(float speed)
        {
            lineSpeed = Math.Max(10f, speed); // Minimum speed to prevent freezing
        }

        /// <summary>
        /// Get current aiming progress (0-1 for horizontal, 0-1 for vertical)
        /// </summary>
        public Vector2 GetAimingProgress()
        {
            float hProgress = (hLinePos.X - aimBox.Left) / (float)aimBox.Width;
            float vProgress = (vLinePos.Y - aimBox.Top) / (float)aimBox.Height;
            return new Vector2(hProgress, vProgress);
        }

        /// <summary>
        /// Check if the current aim is in the center zone (for accuracy bonuses)
        /// </summary>
        public bool IsInCenterZone(float tolerance = 0.2f)
        {
            Vector2 progress = GetAimingProgress();
            float centerDistance = Vector2.Distance(progress, new Vector2(0.5f, 0.5f));
            return centerDistance <= tolerance;
        }

        /// <summary>
        /// Helper method to draw rectangle outline
        /// </summary>
        private void DrawRectangleOutline(SpriteBatch spriteBatch, Texture2D pixelTexture, Rectangle rect, Color color, int thickness)
        {
            // Top
            spriteBatch.Draw(pixelTexture, new Rectangle(rect.X, rect.Y, rect.Width, thickness), color);
            // Bottom
            spriteBatch.Draw(pixelTexture, new Rectangle(rect.X, rect.Bottom - thickness, rect.Width, thickness), color);
            // Left
            spriteBatch.Draw(pixelTexture, new Rectangle(rect.X, rect.Y, thickness, rect.Height), color);
            // Right
            spriteBatch.Draw(pixelTexture, new Rectangle(rect.Right - thickness, rect.Y, thickness, rect.Height), color);
        }

        /// <summary>
        /// Get debug information about current aiming state
        /// </summary>
        public string GetDebugInfo()
        {
            return $"Active: {isActive}, HLocked: {horizLocked}, Pending: {shotPending}, " +
                   $"Target: ({GetTargetPosition().X:F1}, {GetTargetPosition().Y:F1}), Speed: {lineSpeed:F1}";
        }
    }
}
