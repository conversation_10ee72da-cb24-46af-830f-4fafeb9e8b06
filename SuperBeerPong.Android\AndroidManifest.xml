﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="com.companyname.SuperBeerPong" android:versionCode="1" android:versionName="1.0">
	<uses-feature android:glEsVersion="0x00020000" android:required="true" />
	<uses-sdk android:minSdkVersion="29" android:targetSdkVersion="34" />
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
	<application
		android:hardwareAccelerated="true"
		android:icon="@drawable/icon"
		android:isGame="true"
		android:label="@string/app_name"
		android:theme="@style/MainTheme"
		/>
</manifest>