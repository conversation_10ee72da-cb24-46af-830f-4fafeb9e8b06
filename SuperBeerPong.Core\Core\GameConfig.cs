using Microsoft.Xna.Framework;
using System;

namespace SuperBeerPong.Core.Core
{
    /// <summary>
    /// Central configuration for game constants and settings
    /// </summary>
    public static class GameConfig
    {
        // === GAME BALANCE ===
        public const int ShotsPerTurn = 1;
        public const int MainGameCupsPerSide = 10;
        public const int OvertimeCupsPerSide = 3;
        public const int TotalNPCs = 10;

        // === INTOXICATION SYSTEM ===
        public const float IntoxicationPerCup = 0.25f; // Intoxication gained per cup hit
        public const float IntoxicationDecay = 0.03f; // Intoxication lost per frame
        public const float MaxIntoxication = 1.0f; // Maximum intoxication level
        public const float BaseLineSpeed = 80f; // Base aiming line speed
        public const float MaxLineSpeedMultiplier = 3.0f; // Max speed increase from intoxication

        // === PHYSICS ===
        public const float BallFlightTime = 1.5f; // Total ball flight time in seconds
        public const float BallHitRadius = 20f; // Collision radius for ball-cup hits
        public const float BallMaxHeight = 50f; // Maximum arc height for ball trajectory

        // === CUP FORMATION ===
        public const float CupDiameter = 40f;
        public const float CupSpacing = 40f; // Center-to-center distance
        public const float CupRadius = CupDiameter / 2f;
        public const float HexPackingRatio = 0.866f; // For triangular formations

        // === TABLE DIMENSIONS ===
        public const int TableWidth = 512;
        public const int TableHeight = 512;
        public const int TableMarginTop = 60;
        public const int TableMarginBottom = 60;
        public const int TableMarginSides = 50;

        // === UI LAYOUT ===
        public const int ProfileImageSize = 180;
        public const int ProfileImageX = 20;
        public const int ProfileImageY = 20;
        public const int TextMarginLeft = 20;
        public const int TextMarginTop = 210;
        public const int TextWrapWidth = 300;
        public const int MenuOptionSpacing = 40;

        // === AIMING SYSTEM ===
        public const int AimBoxWidth = 200;
        public const int AimBoxHeight = 100;
        public const float AimLineThickness = 2f;

        // === AI DIFFICULTY SCALING ===
        public const float BaseAIAccuracy = 30f; // Base inaccuracy for AI
        public const float DifficultyScaling = 0.8f; // Accuracy improvement per difficulty level
        public const float MinAIInaccuracy = 5f; // Minimum inaccuracy for highest difficulty

        // === MENU CONFIGURATION ===
        public const int MainMenuOptionCount = 4;
        public static readonly string[] MainMenuOptions = 
        {
            "Story Mode",
            "Player vs Computer", 
            "Player vs Player",
            "Exit"
        };

        // === COLORS ===
        public static readonly Color MenuSelectedColor = Color.Yellow;
        public static readonly Color MenuNormalColor = Color.White;
        public static readonly Color MenuTitleColor = Color.Yellow;
        public static readonly Color HUDTextColor = Color.White;
        public static readonly Color GameOverWinColor = Color.Green;
        public static readonly Color GameOverLoseColor = Color.Red;

        // === STORY MODE ===
        public static readonly Vector2 StoryModeProfilePosition = new Vector2(ProfileImageX, ProfileImageY);
        public static readonly Vector2 StoryModeTextPosition = new Vector2(TextMarginLeft, TextMarginTop);
        public static readonly Rectangle StoryModeProfileRect = new Rectangle(
            ProfileImageX, ProfileImageY, ProfileImageSize, ProfileImageSize);

        // === FORMATION POSITIONS ===
        public static readonly int[] TriangleRowCounts = { 4, 3, 2, 1 }; // 10-cup triangle formation
        public static readonly int[] OvertimeRowCounts = { 2, 1 }; // 3-cup overtime formation

        /// <summary>
        /// Calculate intoxication effect on line speed
        /// </summary>
        public static float CalculateLineSpeed(float intoxication)
        {
            return BaseLineSpeed * (1f + intoxication * MaxLineSpeedMultiplier);
        }

        /// <summary>
        /// Calculate AI accuracy based on difficulty and intoxication
        /// </summary>
        public static float CalculateAIInaccuracy(int difficultyLevel, float intoxication)
        {
            float baseInaccuracy = BaseAIAccuracy - (difficultyLevel * DifficultyScaling);
            float intoxicationEffect = intoxication * 20f; // Intoxication makes AI worse
            return Math.Max(MinAIInaccuracy, baseInaccuracy + intoxicationEffect);
        }

        /// <summary>
        /// Get table rectangle for current screen size
        /// </summary>
        public static Rectangle GetTableRectangle(int screenWidth, int screenHeight)
        {
            int tableX = (screenWidth - TableWidth) / 2;
            int tableY = (screenHeight - TableHeight) / 2;
            return new Rectangle(tableX, tableY, TableWidth, TableHeight);
        }

        /// <summary>
        /// Get aiming box rectangle for current screen
        /// </summary>
        public static Rectangle GetAimBoxRectangle(int screenWidth, int screenHeight)
        {
            int aimX = (screenWidth - AimBoxWidth) / 2;
            int aimY = screenHeight - AimBoxHeight - 50;
            return new Rectangle(aimX, aimY, AimBoxWidth, AimBoxHeight);
        }
    }
}
