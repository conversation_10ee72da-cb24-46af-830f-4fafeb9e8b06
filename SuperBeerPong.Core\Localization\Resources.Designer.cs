//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;

namespace SuperBeerPong.Core.Localization
{
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    ///   This class was generated by MSBuild using the GenerateResource task.
    ///   To add or remove a member, edit your .resx file then rerun MSBuild.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Build.Tasks.StronglyTypedResourceBuilder", "15.1.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources
    {

        private static global::System.Resources.ResourceManager resourceMan;

        private static global::System.Globalization.CultureInfo resourceCulture;

        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources()
        {
        }

        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager
        {
            get
            {
                if (object.ReferenceEquals(resourceMan, null))
                {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("SuperBeerPong.Core.Localization.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }

        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture
        {
            get
            {
                return resourceCulture;
            }
            set
            {
                resourceCulture = value;
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to About.
        /// </summary>
        internal static string About
        {
            get
            {
                return ResourceManager.GetString("About", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Back.
        /// </summary>
        internal static string Back
        {
            get
            {
                return ResourceManager.GetString("Back", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Click Anywhere.
        /// </summary>
        internal static string ClickAnywhere
        {
            get
            {
                return ResourceManager.GetString("ClickAnywhere", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Collect These!.
        /// </summary>
        internal static string CollectThese
        {
            get
            {
                return ResourceManager.GetString("CollectThese", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Display Mode : {0}.
        /// </summary>
        internal static string DisplayMode
        {
            get
            {
                return ResourceManager.GetString("DisplayMode", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Don&apos;t Die!.
        /// </summary>
        internal static string DontDie
        {
            get
            {
                return ResourceManager.GetString("DontDie", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to english (English).
        /// </summary>
        internal static string English
        {
            get
            {
                return ResourceManager.GetString("English", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Initialize can only be called once.
        /// </summary>
        internal static string ErrorAccelerometerInitializeOnce
        {
            get
            {
                return ResourceManager.GetString("ErrorAccelerometerInitializeOnce", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to You must Initialize before you can call GetState().
        /// </summary>
        internal static string ErrorAccelerometerMustInitialize
        {
            get
            {
                return ResourceManager.GetString("ErrorAccelerometerMustInitialize", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Acceleration: {0}, IsActive: {1}.
        /// </summary>
        internal static string ErrorAccelerometerToString
        {
            get
            {
                return ResourceManager.GetString("ErrorAccelerometerToString", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to A level must have an exit..
        /// </summary>
        internal static string ErrorLevelExit
        {
            get
            {
                return ResourceManager.GetString("ErrorLevelExit", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The length of line {0} is different from all preceeding lines..
        /// </summary>
        internal static string ErrorLevelLineLength
        {
            get
            {
                return ResourceManager.GetString("ErrorLevelLineLength", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to A level may only have one exit..
        /// </summary>
        internal static string ErrorLevelOneExit
        {
            get
            {
                return ResourceManager.GetString("ErrorLevelOneExit", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to A level may only have one starting point..
        /// </summary>
        internal static string ErrorLevelOneStartingPoint
        {
            get
            {
                return ResourceManager.GetString("ErrorLevelOneStartingPoint", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to A level must have a starting point..
        /// </summary>
        internal static string ErrorLevelStartingPoint
        {
            get
            {
                return ResourceManager.GetString("ErrorLevelStartingPoint", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to No animation is currently playing..
        /// </summary>
        internal static string ErrorNoAnimation
        {
            get
            {
                return ResourceManager.GetString("ErrorNoAnimation", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Unsupported tile type character &apos;{0}&apos; at position {1}, {2}..
        /// </summary>
        internal static string ErrorUnsupportedTileType
        {
            get
            {
                return ResourceManager.GetString("ErrorUnsupportedTileType", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Exit.
        /// </summary>
        internal static string Exit
        {
            get
            {
                return ResourceManager.GetString("Exit", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to exit?.
        /// </summary>
        internal static string ExitQuestion
        {
            get
            {
                return ResourceManager.GetString("ExitQuestion", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Fullscreen.
        /// </summary>
        internal static string FullScreen
        {
            get
            {
                return ResourceManager.GetString("FullScreen", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Gems Collected.
        /// </summary>
        internal static string GemsCollected
        {
            get
            {
                return ResourceManager.GetString("GemsCollected", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Get To Here!.
        /// </summary>
        internal static string GetToHere
        {
            get
            {
                return ResourceManager.GetString("GetToHere", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Language: .
        /// </summary>
        internal static string Language
        {
            get
            {
                return ResourceManager.GetString("Language", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Let&apos;s GO!!!!.
        /// </summary>
        internal static string LetsGo
        {
            get
            {
                return ResourceManager.GetString("LetsGo", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Level Completed!.
        /// </summary>
        internal static string LevelCompleted
        {
            get
            {
                return ResourceManager.GetString("LevelCompleted", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Loading....
        /// </summary>
        internal static string Loading
        {
            get
            {
                return ResourceManager.GetString("Loading", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Main Menu.
        /// </summary>
        internal static string MainMenu
        {
            get
            {
                return ResourceManager.GetString("MainMenu", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to MonoGame Site.
        /// </summary>
        internal static string MonoGameSite
        {
            get
            {
                return ResourceManager.GetString("MonoGameSite", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to CONGRATULATIONS!! You set a new high score..
        /// </summary>
        internal static string NewHighScore
        {
            get
            {
                return ResourceManager.GetString("NewHighScore", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to No = B button, Esc.
        /// </summary>
        internal static string NoButtonHelp
        {
            get
            {
                return ResourceManager.GetString("NoButtonHelp", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to [ No ].
        /// </summary>
        internal static string NoButtonText
        {
            get
            {
                return ResourceManager.GetString("NoButtonText", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Particle Effect: .
        /// </summary>
        internal static string ParticleEffect
        {
            get
            {
                return ResourceManager.GetString("ParticleEffect", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Paused.
        /// </summary>
        internal static string Paused
        {
            get
            {
                return ResourceManager.GetString("Paused", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Play.
        /// </summary>
        internal static string Play
        {
            get
            {
                return ResourceManager.GetString("Play", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Quit.
        /// </summary>
        internal static string Quit
        {
            get
            {
                return ResourceManager.GetString("Quit", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to quit?.
        /// </summary>
        internal static string QuitQuestion
        {
            get
            {
                return ResourceManager.GetString("QuitQuestion", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Resume.
        /// </summary>
        internal static string Resume
        {
            get
            {
                return ResourceManager.GetString("Resume", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to SCORE: .
        /// </summary>
        internal static string Score
        {
            get
            {
                return ResourceManager.GetString("Score", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Settings.
        /// </summary>
        internal static string Settings
        {
            get
            {
                return ResourceManager.GetString("Settings", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Show Virtual GamePad: .
        /// </summary>
        internal static string ShowVirtualGamePad
        {
            get
            {
                return ResourceManager.GetString("ShowVirtualGamePad", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Tap Anywhere.
        /// </summary>
        internal static string TapAnywhere
        {
            get
            {
                return ResourceManager.GetString("TapAnywhere", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Tap To Pause.
        /// </summary>
        internal static string TapToPause
        {
            get
            {
                return ResourceManager.GetString("TapToPause", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to TIME: .
        /// </summary>
        internal static string Time
        {
            get
            {
                return ResourceManager.GetString("Time", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Time Ran Out!.
        /// </summary>
        internal static string TimeRanOut
        {
            get
            {
                return ResourceManager.GetString("TimeRanOut", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Tutorial.
        /// </summary>
        internal static string Tutorial
        {
            get
            {
                return ResourceManager.GetString("Tutorial", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Windowed.
        /// </summary>
        internal static string Windowed
        {
            get
            {
                return ResourceManager.GetString("Windowed", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Yes = A Button, Space, Enter.
        /// </summary>
        internal static string YesButtonHelp
        {
            get
            {
                return ResourceManager.GetString("YesButtonHelp", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to [ Yes ].
        /// </summary>
        internal static string YesButtonText
        {
            get
            {
                return ResourceManager.GetString("YesButtonText", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to You DIED!.
        /// </summary>
        internal static string YouDied
        {
            get
            {
                return ResourceManager.GetString("YouDied", resourceCulture);
            }
        }
    }
}
