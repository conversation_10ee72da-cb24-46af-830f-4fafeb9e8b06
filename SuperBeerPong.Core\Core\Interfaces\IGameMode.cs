using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using SuperBeerPong.Core.Core;

namespace SuperBeerPong.Core.Core.Interfaces
{
    /// <summary>
    /// Interface for all game modes (Story Mode, Player vs Computer, Player vs Player)
    /// </summary>
    public interface IGameMode
    {
        /// <summary>
        /// Game mode type identifier
        /// </summary>
        GameModeType ModeType { get; }

        /// <summary>
        /// Current game phase (MainGame, Rebuttal, Overtime, GameOver)
        /// </summary>
        GamePhase CurrentPhase { get; }

        /// <summary>
        /// Whether this game mode is currently active
        /// </summary>
        bool IsActive { get; }

        /// <summary>
        /// Initialize the game mode with necessary dependencies
        /// </summary>
        void Initialize(GraphicsDevice graphicsDevice, SpriteBatch spriteBatch);

        /// <summary>
        /// Start a new game/match in this mode
        /// </summary>
        void StartGame();

        /// <summary>
        /// Update game logic for this mode
        /// </summary>
        void Update(GameTime gameTime);

        /// <summary>
        /// Handle input specific to this game mode
        /// </summary>
        void HandleInput(GameTime gameTime);

        /// <summary>
        /// Render the game mode
        /// </summary>
        void Draw(GameTime gameTime);

        /// <summary>
        /// End the current game and return results
        /// </summary>
        GameResult EndGame(bool playerWon);

        /// <summary>
        /// Reset the game mode to initial state
        /// </summary>
        void Reset();

        /// <summary>
        /// Pause/unpause the game mode
        /// </summary>
        void SetPaused(bool paused);

        /// <summary>
        /// Get current game state information
        /// </summary>
        GameResult GetCurrentState();

        /// <summary>
        /// Event fired when the game mode wants to exit to menu
        /// </summary>
        event System.Action<GameResult> GameCompleted;

        /// <summary>
        /// Event fired when the game mode wants to transition to another mode
        /// </summary>
        event System.Action<GameModeType> ModeChangeRequested;
    }

    /// <summary>
    /// Extended interface for game modes that support progression (like Story Mode)
    /// </summary>
    public interface IProgressiveGameMode : IGameMode
    {
        /// <summary>
        /// Current progression state
        /// </summary>
        StoryModeProgress Progress { get; }

        /// <summary>
        /// Advance to the next level/opponent
        /// </summary>
        bool AdvanceToNext();

        /// <summary>
        /// Check if there are more levels/opponents
        /// </summary>
        bool HasNext();

        /// <summary>
        /// Reset progression to the beginning
        /// </summary>
        void ResetProgress();

        /// <summary>
        /// Load progression from saved state
        /// </summary>
        void LoadProgress(StoryModeProgress progress);

        /// <summary>
        /// Event fired when progression changes
        /// </summary>
        event System.Action<StoryModeProgress> ProgressChanged;
    }

    /// <summary>
    /// Interface for game modes that support AI opponents
    /// </summary>
    public interface IAIGameMode : IGameMode
    {
        /// <summary>
        /// Current AI difficulty level (1-10)
        /// </summary>
        int AIDifficulty { get; set; }

        /// <summary>
        /// Current AI opponent (if applicable)
        /// </summary>
        object CurrentOpponent { get; }

        /// <summary>
        /// Set the AI opponent for this game
        /// </summary>
        void SetAIOpponent(object opponent);

        /// <summary>
        /// Get AI move/action for current turn
        /// </summary>
        Vector2 GetAIMove();
    }
}
