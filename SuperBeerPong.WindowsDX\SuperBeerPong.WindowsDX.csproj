﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <RollForward>Major</RollForward>
    <PublishReadyToRun>false</PublishReadyToRun>
    <TieredCompilation>false</TieredCompilation>
    <UseWindowsForms>true</UseWindowsForms>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <ApplicationIcon>../SuperBeerPong.Core/Content/Icon.ico</ApplicationIcon>
    <AssemblyName>SuperBeerPong</AssemblyName>
  </PropertyGroup>
  <ItemGroup>
    <MonoGameContentReference Include="..\SuperBeerPong.Core\Content\SuperBeerPong.mgcb">
      <Link>Content\SuperBeerPong.mgcb</Link>
    </MonoGameContentReference>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="MonoGame.Content.Builder.Task" Version="3.8.*" />
    <PackageReference Include="MonoGame.Framework.WindowsDX" Version="3.8.*" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SuperBeerPong.Core\SuperBeerPong.Core.csproj">
      <ReferenceSourceTarget></ReferenceSourceTarget>
    </ProjectReference>
  </ItemGroup>
  <Target Name="RestoreDotnetTools" BeforeTargets="CollectPackageReferences">
    <Message Text="Restoring dotnet tools (this might take a while depending on your internet speed and should only happen upon building your project for the first time, or after upgrading MonoGame, or clearing your nuget cache)" Importance="High" />
    <Exec Command="dotnet tool restore" />
  </Target>
</Project>