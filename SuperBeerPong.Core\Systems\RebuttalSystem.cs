using Microsoft.Xna.Framework;
using SuperBeerPong.Core.Core;
using System;
using System.Collections.Generic;

namespace SuperBeerPong.Core.Systems
{
    /// <summary>
    /// Manages the rebuttal and overtime system for authentic beer pong rules
    /// </summary>
    public class RebuttalSystem
    {
        private Core.GamePhase currentPhase;
        private bool isRebuttalPlayerTurn;
        private int initialPlayerCups;
        private int initialOpponentCups;

        // Events
        public event Action<bool> RebuttalStarted; // bool = isPlayerRebuttal
        public event Action OvertimeStarted;
        public event Action<bool> GameEnded; // bool = playerWon
        public event Action<Core.GamePhase> PhaseChanged;

        // Properties
        public Core.GamePhase CurrentPhase => currentPhase;
        public bool IsRebuttalActive => currentPhase == Core.GamePhase.Rebuttal;
        public bool IsOvertimeActive => currentPhase == Core.GamePhase.Overtime;
        public bool IsRebuttalPlayerTurn => isRebuttalPlayerTurn;

        public RebuttalSystem()
        {
            Reset();
        }

        /// <summary>
        /// Reset the rebuttal system to main game phase
        /// </summary>
        public void Reset()
        {
            SetPhase(Core.GamePhase.MainGame);
            isRebuttalPlayerTurn = false;
            initialPlayerCups = 0;
            initialOpponentCups = 0;
        }

        /// <summary>
        /// Initialize for a new game
        /// </summary>
        public void Initialize(int playerCupCount, int opponentCupCount)
        {
            Reset();
            initialPlayerCups = playerCupCount;
            initialOpponentCups = opponentCupCount;
        }

        /// <summary>
        /// Check game state after a cup is hit and handle phase transitions
        /// </summary>
        public void CheckGameStateAfterCupHit(List<Cup> playerCups, List<Cup> opponentCups)
        {
            int playerCupsRemaining = CountActiveCups(playerCups);
            int opponentCupsRemaining = CountActiveCups(opponentCups);

            switch (currentPhase)
            {
                case Core.GamePhase.MainGame:
                    HandleMainGameCupHit(playerCupsRemaining, opponentCupsRemaining);
                    break;

                case Core.GamePhase.Rebuttal:
                    HandleRebuttalCupHit(playerCupsRemaining, opponentCupsRemaining);
                    break;

                case Core.GamePhase.Overtime:
                    HandleOvertimeCupHit(playerCupsRemaining, opponentCupsRemaining);
                    break;
            }
        }

        /// <summary>
        /// Handle a missed shot during rebuttal
        /// </summary>
        public void HandleRebuttalMiss()
        {
            if (currentPhase != Core.GamePhase.Rebuttal) return;

            // Rebuttal shot missed, game ends
            bool playerWon = !isRebuttalPlayerTurn; // Winner is opposite of rebuttal team
            EndGame(playerWon);
        }

        /// <summary>
        /// Start rebuttal phase
        /// </summary>
        public void StartRebuttal(bool isPlayerRebuttal)
        {
            SetPhase(Core.GamePhase.Rebuttal);
            isRebuttalPlayerTurn = isPlayerRebuttal;
            RebuttalStarted?.Invoke(isPlayerRebuttal);
        }

        /// <summary>
        /// Start overtime phase
        /// </summary>
        public void StartOvertime()
        {
            SetPhase(Core.GamePhase.Overtime);
            OvertimeStarted?.Invoke();
        }

        /// <summary>
        /// End the game
        /// </summary>
        public void EndGame(bool playerWon)
        {
            SetPhase(Core.GamePhase.GameOver);
            GameEnded?.Invoke(playerWon);
        }

        /// <summary>
        /// Check if rebuttal should be triggered
        /// </summary>
        public bool ShouldTriggerRebuttal(List<Cup> playerCups, List<Cup> opponentCups)
        {
            if (currentPhase != Core.GamePhase.MainGame) return false;

            int playerCupsRemaining = CountActiveCups(playerCups);
            int opponentCupsRemaining = CountActiveCups(opponentCups);

            // Rebuttal triggers when one team eliminates ALL opponent cups
            return playerCupsRemaining == 0 || opponentCupsRemaining == 0;
        }

        /// <summary>
        /// Check if overtime should be triggered
        /// </summary>
        public bool ShouldTriggerOvertime(List<Cup> playerCups, List<Cup> opponentCups)
        {
            if (currentPhase != Core.GamePhase.Rebuttal) return false;

            int playerCupsRemaining = CountActiveCups(playerCups);
            int opponentCupsRemaining = CountActiveCups(opponentCups);

            // Overtime triggers when rebuttal team eliminates all remaining cups
            if (isRebuttalPlayerTurn)
            {
                return opponentCupsRemaining == 0; // Player hit all opponent cups
            }
            else
            {
                return playerCupsRemaining == 0; // Opponent hit all player cups
            }
        }

        /// <summary>
        /// Get the team that should take rebuttal shots
        /// </summary>
        public bool GetRebuttalTeam(List<Cup> playerCups, List<Cup> opponentCups)
        {
            int playerCupsRemaining = CountActiveCups(playerCups);
            int opponentCupsRemaining = CountActiveCups(opponentCups);

            if (opponentCupsRemaining == 0)
            {
                // Player eliminated all opponent cups, opponent gets rebuttal
                return false;
            }
            else if (playerCupsRemaining == 0)
            {
                // Opponent eliminated all player cups, player gets rebuttal
                return true;
            }

            return false; // No rebuttal needed
        }

        /// <summary>
        /// Get description of current phase
        /// </summary>
        public string GetPhaseDescription()
        {
            return currentPhase switch
            {
                Core.GamePhase.MainGame => "Main Game",
                Core.GamePhase.Rebuttal => isRebuttalPlayerTurn ? "Player Rebuttal" : "Opponent Rebuttal",
                Core.GamePhase.Overtime => "Sudden Death Overtime",
                Core.GamePhase.GameOver => "Game Over",
                _ => "Unknown Phase"
            };
        }

        /// <summary>
        /// Get instructions for current phase
        /// </summary>
        public string GetPhaseInstructions()
        {
            return currentPhase switch
            {
                Core.GamePhase.MainGame => "Eliminate all opponent cups to win!",
                Core.GamePhase.Rebuttal when isRebuttalPlayerTurn => "Hit all remaining cups to force overtime!",
                Core.GamePhase.Rebuttal when !isRebuttalPlayerTurn => "Opponent attempting rebuttal...",
                Core.GamePhase.Overtime => "First to eliminate all cups wins!",
                Core.GamePhase.GameOver => "Game Complete",
                _ => ""
            };
        }

        /// <summary>
        /// Check if continuous shooting is allowed (during rebuttal)
        /// </summary>
        public bool AllowsContinuousShooting()
        {
            return currentPhase == Core.GamePhase.Rebuttal;
        }

        /// <summary>
        /// Handle main game cup hit
        /// </summary>
        private void HandleMainGameCupHit(int playerCupsRemaining, int opponentCupsRemaining)
        {
            if (opponentCupsRemaining == 0)
            {
                // Player eliminated all opponent cups, opponent gets rebuttal
                StartRebuttal(false);
            }
            else if (playerCupsRemaining == 0)
            {
                // Opponent eliminated all player cups, player gets rebuttal
                StartRebuttal(true);
            }
            // Otherwise, game continues normally
        }

        /// <summary>
        /// Handle rebuttal phase cup hit
        /// </summary>
        private void HandleRebuttalCupHit(int playerCupsRemaining, int opponentCupsRemaining)
        {
            if (isRebuttalPlayerTurn && opponentCupsRemaining == 0)
            {
                // Player completed rebuttal successfully, go to overtime
                StartOvertime();
            }
            else if (!isRebuttalPlayerTurn && playerCupsRemaining == 0)
            {
                // Opponent completed rebuttal successfully, go to overtime
                StartOvertime();
            }
            // If cups remain, rebuttal continues (continuous shooting)
        }

        /// <summary>
        /// Handle overtime phase cup hit
        /// </summary>
        private void HandleOvertimeCupHit(int playerCupsRemaining, int opponentCupsRemaining)
        {
            if (opponentCupsRemaining == 0)
            {
                // Player wins overtime
                EndGame(true);
            }
            else if (playerCupsRemaining == 0)
            {
                // Opponent wins overtime
                EndGame(false);
            }
            // Otherwise, overtime continues
        }

        /// <summary>
        /// Set the current phase and fire event
        /// </summary>
        private void SetPhase(Core.GamePhase newPhase)
        {
            if (currentPhase != newPhase)
            {
                currentPhase = newPhase;
                PhaseChanged?.Invoke(currentPhase);
            }
        }

        /// <summary>
        /// Count active cups in a list
        /// </summary>
        private int CountActiveCups(List<Cup> cups)
        {
            int count = 0;
            foreach (Cup cup in cups)
            {
                if (cup.IsActive) count++;
            }
            return count;
        }

        /// <summary>
        /// Get debug information about current rebuttal state
        /// </summary>
        public string GetDebugInfo()
        {
            return $"Phase: {currentPhase}, RebuttalPlayer: {isRebuttalPlayerTurn}, " +
                   $"Description: {GetPhaseDescription()}";
        }

        /// <summary>
        /// Check if the game is in a final state
        /// </summary>
        public bool IsGameComplete()
        {
            return currentPhase == Core.GamePhase.GameOver;
        }

        /// <summary>
        /// Force end the game (for testing or special conditions)
        /// </summary>
        public void ForceEndGame(bool playerWon)
        {
            EndGame(playerWon);
        }
    }
}
