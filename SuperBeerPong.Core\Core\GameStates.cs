using Microsoft.Xna.Framework;

namespace SuperBeerPong.Core.Core
{
    /// <summary>
    /// Main game states for the application
    /// </summary>
    public enum GameState
    {
        MainMenu,    // Start menu with options
        Playing,     // In-game (Beer Pong gameplay)
        GameOver     // Final results screen
    }

    /// <summary>
    /// Game phases within a match
    /// </summary>
    public enum GamePhase
    {
        MainGame,    // Normal 10-cup game
        Rebuttal,    // Losing team shooting to force overtime
        Overtime,    // 3-cup sudden death
        GameOver     // Final winner determined
    }

    /// <summary>
    /// Available game modes
    /// </summary>
    public enum GameModeType
    {
        StoryMode,
        PlayerVsComputer,
        PlayerVsPlayer
    }

    /// <summary>
    /// Menu navigation states
    /// </summary>
    public enum MenuState
    {
        Main,
        GameModeSelection,
        StoryModeProgress,
        Settings,
        GameOver
    }

    /// <summary>
    /// Input action types for consistent input handling
    /// </summary>
    public enum InputAction
    {
        MenuUp,
        MenuDown,
        MenuSelect,
        MenuBack,
        Shoot,
        Aim,
        Pause,
        Exit
    }

    /// <summary>
    /// Game result information
    /// </summary>
    public struct GameResult
    {
        public bool PlayerWon { get; set; }
        public int PlayerCupsRemaining { get; set; }
        public int OpponentCupsRemaining { get; set; }
        public GamePhase FinalPhase { get; set; }
        public float PlayerIntoxication { get; set; }
        public float OpponentIntoxication { get; set; }
        public int TotalShots { get; set; }
        public bool WasRebuttal { get; set; }

        public GameResult(bool playerWon, int playerCups, int opponentCups, GamePhase phase)
        {
            PlayerWon = playerWon;
            PlayerCupsRemaining = playerCups;
            OpponentCupsRemaining = opponentCups;
            FinalPhase = phase;
            PlayerIntoxication = 0f;
            OpponentIntoxication = 0f;
            TotalShots = 0;
            WasRebuttal = false;
        }
    }

    /// <summary>
    /// Story mode progression data
    /// </summary>
    public struct StoryModeProgress
    {
        public int CurrentNPCNumber { get; set; }
        public int HighestNPCReached { get; set; }
        public bool IsCompleted { get; set; }
        public int TotalWins { get; set; }
        public int TotalLosses { get; set; }

        public StoryModeProgress(int currentNPC = 1)
        {
            CurrentNPCNumber = currentNPC;
            HighestNPCReached = currentNPC;
            IsCompleted = false;
            TotalWins = 0;
            TotalLosses = 0;
        }
    }
}
