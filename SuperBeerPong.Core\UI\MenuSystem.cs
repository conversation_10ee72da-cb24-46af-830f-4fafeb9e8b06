using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using SuperBeerPong.Core.Core;
using SuperBeerPong.Core.Core.Interfaces;
using System;
using System.Collections.Generic;

namespace SuperBeerPong.Core.UI
{
    /// <summary>
    /// Main menu system coordinator that manages different menu screens
    /// </summary>
    public class MenuSystem : IMenuSystem
    {
        private Dictionary<MenuState, IMenuScreen> menuScreens;
        private Stack<MenuState> navigationStack;
        private MenuState currentState;
        private bool isActive;
        
        private GraphicsDevice graphicsDevice;
        private SpriteBatch spriteBatch;
        private SpriteFont font;
        private IInputHandler inputHandler;

        // Events
        public event Action<GameModeType> GameModeSelected;
        public event Action ExitRequested;
        public event Action SettingsRequested;

        // Properties
        public MenuState CurrentState => currentState;
        public bool IsActive => isActive;

        public MenuSystem(IInputHandler inputHandler)
        {
            this.inputHandler = inputHandler;
            menuScreens = new Dictionary<MenuState, IMenuScreen>();
            navigationStack = new Stack<MenuState>();
            currentState = MenuState.Main;
            isActive = true;
        }

        /// <summary>
        /// Initialize the menu system with graphics resources
        /// </summary>
        public void Initialize(GraphicsDevice graphicsDevice, SpriteBatch spriteBatch)
        {
            this.graphicsDevice = graphicsDevice;
            this.spriteBatch = spriteBatch;

            // Create menu screens
            CreateMenuScreens();

            // Initialize all screens
            foreach (var screen in menuScreens.Values)
            {
                screen.Initialize();
                screen.NavigationRequested += OnNavigationRequested;
                screen.ActionCompleted += OnActionCompleted;
            }

            // Start with main menu
            NavigateTo(MenuState.Main);
        }

        /// <summary>
        /// Set the font for menu rendering
        /// </summary>
        public void SetFont(SpriteFont font)
        {
            this.font = font;
        }

        /// <summary>
        /// Update the current menu screen
        /// </summary>
        public void Update(GameTime gameTime)
        {
            if (!isActive) return;

            // Handle global menu input
            HandleGlobalInput();

            // Update current screen
            if (menuScreens.TryGetValue(currentState, out IMenuScreen currentScreen))
            {
                currentScreen.Update(gameTime);
                currentScreen.HandleInput(gameTime);
            }
        }

        /// <summary>
        /// Render the current menu screen
        /// </summary>
        public void Draw(GameTime gameTime)
        {
            if (!isActive) return;

            if (menuScreens.TryGetValue(currentState, out IMenuScreen currentScreen))
            {
                currentScreen.Draw(spriteBatch, font);
            }
        }

        /// <summary>
        /// Navigate to a specific menu state
        /// </summary>
        public void NavigateTo(MenuState state)
        {
            // Exit current screen
            if (menuScreens.TryGetValue(currentState, out IMenuScreen currentScreen))
            {
                currentScreen.OnExit();
            }

            // Push current state to navigation stack (for back navigation)
            if (currentState != state)
            {
                navigationStack.Push(currentState);
            }

            // Set new state
            currentState = state;

            // Enter new screen
            if (menuScreens.TryGetValue(currentState, out IMenuScreen newScreen))
            {
                newScreen.OnEnter();
            }
        }

        /// <summary>
        /// Go back to the previous menu
        /// </summary>
        public void GoBack()
        {
            if (navigationStack.Count > 0)
            {
                MenuState previousState = navigationStack.Pop();
                
                // Exit current screen
                if (menuScreens.TryGetValue(currentState, out IMenuScreen currentScreen))
                {
                    currentScreen.OnExit();
                }

                // Set previous state
                currentState = previousState;

                // Enter previous screen
                if (menuScreens.TryGetValue(currentState, out IMenuScreen previousScreen))
                {
                    previousScreen.OnEnter();
                }
            }
            else if (currentState != MenuState.Main)
            {
                // If no navigation history, go to main menu
                NavigateTo(MenuState.Main);
            }
        }

        /// <summary>
        /// Show the menu system
        /// </summary>
        public void Show()
        {
            isActive = true;
            
            // Re-enter current screen
            if (menuScreens.TryGetValue(currentState, out IMenuScreen currentScreen))
            {
                currentScreen.OnEnter();
            }
        }

        /// <summary>
        /// Hide the menu system
        /// </summary>
        public void Hide()
        {
            isActive = false;
            
            // Exit current screen
            if (menuScreens.TryGetValue(currentState, out IMenuScreen currentScreen))
            {
                currentScreen.OnExit();
            }
        }

        /// <summary>
        /// Reset menu to initial state
        /// </summary>
        public void Reset()
        {
            navigationStack.Clear();
            NavigateTo(MenuState.Main);
        }

        /// <summary>
        /// Create all menu screens
        /// </summary>
        private void CreateMenuScreens()
        {
            menuScreens[MenuState.Main] = new MainMenu(inputHandler);
            menuScreens[MenuState.GameOver] = new GameOverScreen(inputHandler);
            // Add more screens as needed
        }

        /// <summary>
        /// Handle global menu input (like back/escape)
        /// </summary>
        private void HandleGlobalInput()
        {
            if (inputHandler.IsMenuBackTriggered())
            {
                if (currentState == MenuState.Main)
                {
                    // Exit application from main menu
                    ExitRequested?.Invoke();
                }
                else
                {
                    // Go back to previous menu
                    GoBack();
                }
            }
        }

        /// <summary>
        /// Handle navigation requests from menu screens
        /// </summary>
        private void OnNavigationRequested(MenuState targetState)
        {
            NavigateTo(targetState);
        }

        /// <summary>
        /// Handle action completion from menu screens
        /// </summary>
        private void OnActionCompleted(object result)
        {
            switch (result)
            {
                case GameModeType gameMode:
                    GameModeSelected?.Invoke(gameMode);
                    break;
                
                case "Exit":
                    ExitRequested?.Invoke();
                    break;
                
                case "Settings":
                    SettingsRequested?.Invoke();
                    break;
            }
        }

        /// <summary>
        /// Add a custom menu screen
        /// </summary>
        public void AddMenuScreen(MenuState state, IMenuScreen screen)
        {
            menuScreens[state] = screen;
            screen.Initialize();
            screen.NavigationRequested += OnNavigationRequested;
            screen.ActionCompleted += OnActionCompleted;
        }

        /// <summary>
        /// Remove a menu screen
        /// </summary>
        public void RemoveMenuScreen(MenuState state)
        {
            if (menuScreens.TryGetValue(state, out IMenuScreen screen))
            {
                screen.NavigationRequested -= OnNavigationRequested;
                screen.ActionCompleted -= OnActionCompleted;
                menuScreens.Remove(state);
            }
        }

        /// <summary>
        /// Get the current menu screen
        /// </summary>
        public IMenuScreen GetCurrentScreen()
        {
            return menuScreens.TryGetValue(currentState, out IMenuScreen screen) ? screen : null;
        }

        /// <summary>
        /// Check if we can go back
        /// </summary>
        public bool CanGoBack()
        {
            return navigationStack.Count > 0 || currentState != MenuState.Main;
        }
    }
}
