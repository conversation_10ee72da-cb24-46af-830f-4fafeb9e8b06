using Microsoft.Xna.Framework;
using SuperBeerPong.Core.Core;
using System;

namespace SuperBeerPong.Core
{
    /// <summary>
    /// Represents a beer pong ball with 3D trajectory simulation
    /// </summary>
    public struct Ball
    {
        public Vector2 Pos;
        public float Z; // Z position for depth simulation
        public Vector2 StartPos, TargetPos; // For trajectory calculation
        public float FlightTime, MaxFlightTime; // For arc calculation
        public bool IsPlayerBall; // Track which player shot this ball

        public Ball(Vector2 start, Vector2 target, bool isPlayerBall = true)
        {
            StartPos = start;
            TargetPos = target;
            Pos = start;
            Z = 0f; // Start at table level
            IsPlayerBall = isPlayerBall;

            // Calculate flight time for realistic arc
            MaxFlightTime = GameConfig.BallFlightTime;
            FlightTime = 0f;
        }

        /// <summary>
        /// Update ball position and trajectory
        /// </summary>
        public void Update(float deltaTime)
        {
            if (!IsActive()) return;

            FlightTime += deltaTime;
            float progress = FlightTime / MaxFlightTime;

            // Linear interpolation for X and Y
            Pos = Vector2.Lerp(StartPos, TargetPos, progress);

            // Parabolic arc for Z (height)
            // Peak at 50% of flight time, back to 0 at end
            float heightProgress = 1f - 4f * (progress - 0.5f) * (progress - 0.5f);
            Z = Math.Max(0f, heightProgress * GameConfig.BallMaxHeight);
        }

        /// <summary>
        /// Get the visual scale based on Z position (depth simulation)
        /// </summary>
        public float GetScale()
        {
            // Scale from 0.5 to 1.0 based on height (higher = smaller)
            float heightRatio = Z / GameConfig.BallMaxHeight;
            return 1.0f - (heightRatio * 0.3f); // Scale down by max 30% at peak
        }

        /// <summary>
        /// Check if ball is still in flight
        /// </summary>
        public bool IsActive()
        {
            return FlightTime < MaxFlightTime;
        }

        /// <summary>
        /// Check if ball is close enough to table level for cup collision
        /// </summary>
        public bool CanHitCups()
        {
            return Z < 20f; // Ball must be close to table level
        }

        /// <summary>
        /// Get the ball's current velocity vector
        /// </summary>
        public Vector2 GetVelocity()
        {
            if (!IsActive()) return Vector2.Zero;

            float deltaTime = 1f / 60f; // Assume 60 FPS for velocity calculation
            float futureProgress = Math.Min(1f, (FlightTime + deltaTime) / MaxFlightTime);
            Vector2 futurePos = Vector2.Lerp(StartPos, TargetPos, futureProgress);
            
            return (futurePos - Pos) / deltaTime;
        }

        /// <summary>
        /// Check collision with a cup
        /// </summary>
        public bool CheckCollisionWithCup(Cup cup)
        {
            if (!CanHitCups()) return false;
            
            float distance = Vector2.Distance(Pos, cup.Pos);
            return distance < GameConfig.BallHitRadius;
        }

        /// <summary>
        /// Check if ball is out of bounds
        /// </summary>
        public bool IsOutOfBounds(Rectangle tableBounds)
        {
            return Pos.X < tableBounds.Left - 50 || 
                   Pos.X > tableBounds.Right + 50 ||
                   Pos.Y < tableBounds.Top - 50 || 
                   Pos.Y > tableBounds.Bottom + 50;
        }

        /// <summary>
        /// Get the ball's position for rendering (with depth offset)
        /// </summary>
        public Vector2 GetRenderPosition()
        {
            // Slight offset based on height for depth illusion
            return new Vector2(Pos.X, Pos.Y - Z * 0.3f);
        }

        /// <summary>
        /// Create a ball with random trajectory variation (for AI shots)
        /// </summary>
        public static Ball CreateWithVariation(Vector2 start, Vector2 target, float inaccuracy, Random random, bool isPlayerBall = false)
        {
            // Add random offset to target based on inaccuracy
            float offsetX = (float)(random.NextDouble() - 0.5) * 2f * inaccuracy;
            float offsetY = (float)(random.NextDouble() - 0.5) * 2f * inaccuracy;
            Vector2 adjustedTarget = target + new Vector2(offsetX, offsetY);

            return new Ball(start, adjustedTarget, isPlayerBall);
        }

        /// <summary>
        /// Get trajectory points for rendering ball path preview
        /// </summary>
        public Vector2[] GetTrajectoryPoints(int pointCount = 20)
        {
            Vector2[] points = new Vector2[pointCount];
            
            for (int i = 0; i < pointCount; i++)
            {
                float progress = (float)i / (pointCount - 1);
                Vector2 pos = Vector2.Lerp(StartPos, TargetPos, progress);
                
                // Add height offset for trajectory visualization
                float heightProgress = 1f - 4f * (progress - 0.5f) * (progress - 0.5f);
                float height = Math.Max(0f, heightProgress * GameConfig.BallMaxHeight);
                
                points[i] = new Vector2(pos.X, pos.Y - height * 0.3f);
            }
            
            return points;
        }
    }
}
