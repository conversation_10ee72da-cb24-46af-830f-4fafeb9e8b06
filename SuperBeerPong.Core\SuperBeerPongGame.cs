using System;
using System.Collections.Generic;
using System.Globalization;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Microsoft.Xna.Framework.Input;
using SuperBeerPong.Core;
using SuperBeerPong.Core.Localization;
using SuperBeerPong.Core.Core;

namespace SuperBeerPong.Core
{
    /// <summary>
    /// The main class for the game, responsible for managing game components, settings, 
    /// and platform-specific configurations.
    /// </summary>
    public class SuperBeerPongGame : Game
    {
        // Resources for drawing
        private GraphicsDeviceManager graphicsDeviceManager;
        private SpriteBatch spriteBatch;

        // Textures
        private Texture2D texTable, texCup, texBall, texPixel;
        private Dictionary<string, Texture2D> profileTextures = new Dictionary<string, Texture2D>();
        private SpriteFont hudFont;

        // Game objects
        private Rectangle tableRect;
        private List<Cup> playerCups = new List<Cup>(); // Player's cups (top of table)
        private List<Cup> opponentCups = new List<Cup>(); // Opponent's cups (bottom of table)
        private List<Ball> balls = new List<Ball>();

        // Game state
        private GameState currentState = GameState.MainMenu;
        private bool isPlayerTurn = true; // true = player's turn, false = opponent's turn
        private int shotsThisTurn = 0;
        private const int shotsPerTurn = 1; // 1 shot per team per turn
        private bool isPlayerVsComputer = true; // true = vs AI, false = vs human player

        // Story mode state
        private bool isStoryMode = false;
        private int currentNPCNumber = 1; // Current NPC opponent (1-10)
        private NPC currentNPC = null;
        private int storyModeProgress = 1; // Tracks furthest NPC reached

        // Rebuttal and overtime system
        private GamePhase currentPhase = GamePhase.MainGame;
        private bool isRebuttalActive = false;
        private bool isRebuttalPlayerTurn = false; // Who is taking rebuttal shots
        private bool gameComplete = false;

        // Menu system
        private int selectedMenuOption = 0;
        private const int menuOptionCount = 4; // Story Mode, Player vs Computer, Player vs Player, Exit
        private KeyboardState previousKeyboardState;

        // Intoxication system (more aggressive)
        private float playerIntoxication = 0f; // 0.0 to 1.0 (0% to 100%)
        private float opponentIntoxication = 0f; // 0.0 to 1.0 (0% to 100%)
        private const float intoxicationPerCup = 0.25f; // Much more intoxication per cup hit (was 0.15f)
        private const float intoxicationDecay = 0.03f; // Slower recovery (was 0.05f)
        private const float baseLineSpeed = 80f; // Slower base speed for more dramatic effect (was 100f)

        // Aiming system
        private Rectangle aimBox;
        private Vector2 hLinePos, vLinePos;
        private float lineSpeed = 100f; // Current line speed (affected by intoxication)
        private bool horizLocked = false, shotPending = false;

        // Input handling
        private MouseState previousMouseState;

        /// <summary>
        /// Indicates if the game is running on a mobile platform.
        /// </summary>
        public readonly static bool IsMobile = OperatingSystem.IsAndroid() || OperatingSystem.IsIOS();

        /// <summary>
        /// Indicates if the game is running on a desktop platform.
        /// </summary>
        public readonly static bool IsDesktop = OperatingSystem.IsMacOS() || OperatingSystem.IsLinux() || OperatingSystem.IsWindows();

        /// <summary>
        /// Initializes a new instance of the game. Configures platform-specific settings, 
        /// initializes services like settings and leaderboard managers, and sets up the 
        /// screen manager for screen transitions.
        /// </summary>
        public SuperBeerPongGame()
        {
            graphicsDeviceManager = new GraphicsDeviceManager(this);

            // Share GraphicsDeviceManager as a service.
            Services.AddService(typeof(GraphicsDeviceManager), graphicsDeviceManager);

            Content.RootDirectory = "Content";

            // Configure screen orientations.
            graphicsDeviceManager.SupportedOrientations = DisplayOrientation.LandscapeLeft | DisplayOrientation.LandscapeRight;

            // Set preferred screen size
            graphicsDeviceManager.PreferredBackBufferWidth = 800;
            graphicsDeviceManager.PreferredBackBufferHeight = 600;

            IsMouseVisible = true;
        }

        /// <summary>
        /// Initializes the game, including setting up localization and adding the 
        /// initial screens to the ScreenManager.
        /// </summary>
        protected override void Initialize()
        {
            base.Initialize();

            // Load supported languages and set the default language.
            List<CultureInfo> cultures = LocalizationManager.GetSupportedCultures();
            var languages = new List<CultureInfo>();
            for (int i = 0; i < cultures.Count; i++)
            {
                languages.Add(cultures[i]);
            }

            // TODO You should load this from a settings file or similar,
            // based on what the user or operating system selected.
            var selectedLanguage = LocalizationManager.DEFAULT_CULTURE_CODE;
            LocalizationManager.SetCulture(selectedLanguage);

            // Initialize game layout
            InitializeGame();
        }

        /// <summary>
        /// Loads game content, such as textures and particle systems.
        /// </summary>
        protected override void LoadContent()
        {
            base.LoadContent();

            // Create SpriteBatch for drawing
            spriteBatch = new SpriteBatch(GraphicsDevice);

            // Load textures from Content/Sprites folder
            texTable = Content.Load<Texture2D>("Sprites/table");
            texCup = Content.Load<Texture2D>("Sprites/cup");
            texBall = Content.Load<Texture2D>("Sprites/ball");

            // Load profile textures for NPCs
            try
            {
                profileTextures["profile-brian"] = Content.Load<Texture2D>("Sprites/profile-brian");
                profileTextures["profile-tony"] = Content.Load<Texture2D>("Sprites/profile-tony");
            }
            catch (Exception ex)
            {
                // If profile images fail to load, use cup texture as fallback
                profileTextures["profile-brian"] = texCup;
                profileTextures["profile-tony"] = texCup;
            }

            // Create a simple 1x1 white pixel texture for drawing lines
            texPixel = new Texture2D(GraphicsDevice, 1, 1);
            texPixel.SetData(new[] { Color.White });

            // Load font
            hudFont = Content.Load<SpriteFont>("Fonts/Hud");
        }

        /// <summary>
        /// Updates the game's logic, called once per frame.
        /// </summary>
        /// <param name="gameTime">
        /// Provides a snapshot of timing values used for game updates.
        /// </param>
        private void InitializeGame()
        {
            // Beer pong table rectangle in center
            tableRect = new Rectangle(260, 20, 280, 560);

            // Aim box (bottom-left corner)
            aimBox = new Rectangle(20, 400, 180, 180);

            // Initialize aiming lines - start at center of aim box
            hLinePos = new Vector2(0, aimBox.Height / 2f); // Start horizontal line in middle
            vLinePos = new Vector2(aimBox.Width / 2f, 0);  // Start vertical line in middle

            // Reset line movement and intoxication
            horizLocked = false;
            shotPending = false;

            // Initialize game state
            isPlayerTurn = true;
            shotsThisTurn = 0;

            // Reset intoxication levels
            playerIntoxication = 0f;
            opponentIntoxication = 0f;
            UpdateLineSpeed(); // Set initial line speed

            // Clear existing cups
            playerCups.Clear();
            opponentCups.Clear();

            // Generate proper Beer Pong formations (4-3-2-1 triangles facing each other)
            PlacePlayerTriangle(); // Top of table (player's cups to defend)
            PlaceOpponentTriangle(); // Bottom of table (opponent's cups to attack)

            // Reset game phase
            currentPhase = GamePhase.MainGame;
            isRebuttalActive = false;
            gameComplete = false;
        }

        private void PlacePlayerTriangle()
        {
            // Player's cups at bottom of table (4-3-2-1 formation, base facing opponent)
            float cupDiameter = 40f; // Cup size
            float cupRadius = cupDiameter / 2f;
            float spacing = cupDiameter; // Center-to-center distance for touching brims
            Vector2 baseCenter = new Vector2(tableRect.Center.X, tableRect.Bottom - 60);

            int[] rowCounts = { 4, 3, 2, 1 }; // Base at bottom, point toward opponent
            float currentY = baseCenter.Y;

            foreach (int cupsInRow in rowCounts)
            {
                float rowWidth = (cupsInRow - 1) * spacing;
                float startX = baseCenter.X - rowWidth / 2f;

                for (int i = 0; i < cupsInRow; i++)
                {
                    playerCups.Add(new Cup(new Vector2(startX + i * spacing, currentY)));
                }
                currentY -= spacing * 0.866f; // Hexagonal packing toward center (upward)
            }
        }

        private void PlaceOpponentTriangle()
        {
            // Opponent's cups at top of table (4-3-2-1 formation, base facing player)
            float cupDiameter = 40f; // Cup size
            float cupRadius = cupDiameter / 2f;
            float spacing = cupDiameter; // Center-to-center distance for touching brims
            Vector2 baseCenter = new Vector2(tableRect.Center.X, tableRect.Top + 60);

            int[] rowCounts = { 4, 3, 2, 1 }; // Base at top, point toward player
            float currentY = baseCenter.Y;

            foreach (int cupsInRow in rowCounts)
            {
                float rowWidth = (cupsInRow - 1) * spacing;
                float startX = baseCenter.X - rowWidth / 2f;

                for (int i = 0; i < cupsInRow; i++)
                {
                    opponentCups.Add(new Cup(new Vector2(startX + i * spacing, currentY)));
                }
                currentY += spacing * 0.866f; // Hexagonal packing toward center (downward)
            }
        }

        private void PlaceOvertimeCups()
        {
            // Clear existing cups
            playerCups.Clear();
            opponentCups.Clear();

            // Place 3 cups in mini triangle formation (2-1) for each team
            float cupDiameter = 40f;
            float spacing = cupDiameter;

            // Player's 3 cups at bottom (2-1 formation, point up)
            Vector2 playerBase = new Vector2(tableRect.Center.X, tableRect.Bottom - 80);
            // Bottom row (2 cups)
            playerCups.Add(new Cup(new Vector2(playerBase.X - spacing/2, playerBase.Y)));
            playerCups.Add(new Cup(new Vector2(playerBase.X + spacing/2, playerBase.Y)));
            // Top cup (1 cup)
            playerCups.Add(new Cup(new Vector2(playerBase.X, playerBase.Y - spacing * 0.866f)));

            // Opponent's 3 cups at top (2-1 formation, point down)
            Vector2 opponentBase = new Vector2(tableRect.Center.X, tableRect.Top + 80);
            // Top row (2 cups)
            opponentCups.Add(new Cup(new Vector2(opponentBase.X - spacing/2, opponentBase.Y)));
            opponentCups.Add(new Cup(new Vector2(opponentBase.X + spacing/2, opponentBase.Y)));
            // Bottom cup (1 cup)
            opponentCups.Add(new Cup(new Vector2(opponentBase.X, opponentBase.Y + spacing * 0.866f)));
        }

        protected override void Update(GameTime gameTime)
        {
            var keyboardState = Keyboard.GetState();
            var mouseState = Mouse.GetState();

            // Exit game or return to menu
            if (GamePad.GetState(PlayerIndex.One).Buttons.Back == ButtonState.Pressed
                || keyboardState.IsKeyDown(Keys.Escape))
            {
                if (currentState == GameState.MainMenu)
                    Exit();
                else
                {
                    // Return to main menu from game
                    currentState = GameState.MainMenu;
                    selectedMenuOption = 0;
                }
            }

            // Handle different game states
            if (currentState == GameState.MainMenu)
            {
                UpdateMainMenu(keyboardState, mouseState);
            }
            else if (currentState == GameState.Playing)
            {
                UpdateGameplay(gameTime, keyboardState, mouseState);
            }

            base.Update(gameTime);
        }

        private void UpdateMainMenu(KeyboardState keyboardState, MouseState mouseState)
        {
            // Handle menu navigation with keyboard
            if (keyboardState.IsKeyDown(Keys.Up) && !previousKeyboardState.IsKeyDown(Keys.Up))
            {
                selectedMenuOption = (selectedMenuOption - 1 + menuOptionCount) % menuOptionCount;
            }
            else if (keyboardState.IsKeyDown(Keys.Down) && !previousKeyboardState.IsKeyDown(Keys.Down))
            {
                selectedMenuOption = (selectedMenuOption + 1) % menuOptionCount;
            }
            else if (keyboardState.IsKeyDown(Keys.Enter) && !previousKeyboardState.IsKeyDown(Keys.Enter))
            {
                SelectMenuOption();
            }

            // Handle menu selection with mouse clicks
            // (We'll implement mouse menu selection later)

            previousKeyboardState = keyboardState;
        }

        private void SelectMenuOption()
        {
            switch (selectedMenuOption)
            {
                case 0: // Story Mode
                    isStoryMode = true;
                    isPlayerVsComputer = true;
                    currentNPCNumber = storyModeProgress; // Continue from current progress
                    currentNPC = NPCFactory.GetNPC(currentNPCNumber);
                    StartNewGame();
                    break;
                case 1: // Player vs Computer
                    isStoryMode = false;
                    isPlayerVsComputer = true;
                    currentNPC = null;
                    StartNewGame();
                    break;
                case 2: // Player vs Player
                    isStoryMode = false;
                    isPlayerVsComputer = false;
                    currentNPC = null;
                    StartNewGame();
                    break;
                case 3: // Exit
                    Exit();
                    break;
            }
        }

        private void StartNewGame()
        {
            currentState = GameState.Playing;
            InitializeGame();
        }

        private void UpdateGameplay(GameTime gameTime, KeyboardState keyboardState, MouseState mouseState)
        {
            float dt = (float)gameTime.ElapsedGameTime.TotalSeconds;

            // === GAME OVER HANDLING ===
            if (currentPhase == GamePhase.GameOver)
            {
                // Handle story mode progression
                if (isStoryMode)
                {
                    // Check for input to continue to next NPC or return to menu
                    if (keyboardState.IsKeyDown(Keys.Enter) && !previousKeyboardState.IsKeyDown(Keys.Enter))
                    {
                        if (opponentCups.Count == 0 && playerCups.Count > 0) // Player won
                        {
                            if (currentNPCNumber <= NPCFactory.GetTotalNPCCount())
                            {
                                // Continue to next NPC
                                ContinueStoryMode();
                            }
                            else
                            {
                                // Story mode completed, return to menu
                                currentState = GameState.MainMenu;
                                selectedMenuOption = 0;
                                isStoryMode = false;
                            }
                        }
                        else // Player lost
                        {
                            // Restart current NPC
                            InitializeGame();
                        }
                    }
                }
                else
                {
                    // Non-story mode: Enter to restart, handled elsewhere
                    if (keyboardState.IsKeyDown(Keys.Enter) && !previousKeyboardState.IsKeyDown(Keys.Enter))
                    {
                        InitializeGame();
                    }
                }
                return; // Don't process other gameplay logic when game is over
            }

            // === AIM LOGIC === (only during player's turn and not game over)
            if (isPlayerTurn && !horizLocked && currentPhase != GamePhase.GameOver) // phase 1: horizontal line moving left/right
            {
                // Move horizontal line position (the line itself moves left/right)
                hLinePos.X += lineSpeed * dt;

                // Bounce at boundaries (preserve speed magnitude from intoxication)
                float currentSpeed = Math.Abs(lineSpeed);
                if (hLinePos.X >= aimBox.Width)
                {
                    hLinePos.X = aimBox.Width;
                    lineSpeed = -currentSpeed; // ensure negative but keep magnitude
                }
                else if (hLinePos.X <= 0)
                {
                    hLinePos.X = 0;
                    lineSpeed = currentSpeed; // ensure positive but keep magnitude
                }

                // Click to lock horizontal position and start vertical movement
                if (mouseState.LeftButton == ButtonState.Pressed && previousMouseState.LeftButton == ButtonState.Released)
                {
                    horizLocked = true;
                    vLinePos.X = hLinePos.X; // lock the X position for vertical line
                    vLinePos.Y = 0; // start vertical line at top
                    lineSpeed = currentSpeed; // preserve speed for vertical movement
                }
            }
            else if (isPlayerTurn && !shotPending && currentPhase != GamePhase.GameOver) // phase 2: vertical line moving up/down
            {
                // Move vertical line position (the line itself moves up/down)
                vLinePos.Y += lineSpeed * dt;

                // Bounce at boundaries (preserve speed magnitude from intoxication)
                float currentSpeed = Math.Abs(lineSpeed);
                if (vLinePos.Y >= aimBox.Height)
                {
                    vLinePos.Y = aimBox.Height;
                    lineSpeed = -currentSpeed; // ensure negative but keep magnitude
                }
                else if (vLinePos.Y <= 0)
                {
                    vLinePos.Y = 0;
                    lineSpeed = currentSpeed; // ensure positive but keep magnitude
                }

                // Click to fire ball at intersection
                if (mouseState.LeftButton == ButtonState.Pressed && previousMouseState.LeftButton == ButtonState.Released)
                {
                    shotPending = true;
                    FireBall();
                }
            }

            // === BALL & COLLISION ===
            for (int i = balls.Count - 1; i >= 0; i--)
            {
                var b = balls[i];
                b.Update(dt);

                // Check if ball is still active
                if (!b.IsActive())
                {
                    balls.RemoveAt(i);
                    continue;
                }

                // Collision with cups (only when ball is near table level)
                if (b.Z < 20f) // Ball is close to table level
                {
                    // Player targets opponent's cups, opponent targets player's cups
                    var targetCups = isPlayerTurn ? opponentCups : playerCups;

                    for (int c = targetCups.Count - 1; c >= 0; c--)
                    {
                        if (Vector2.Distance(b.Pos, targetCups[c].Pos) < 20f) // Hit radius
                        {
                            targetCups.RemoveAt(c);
                            balls.RemoveAt(i);

                            // Add intoxication to the player who got hit
                            if (isPlayerTurn)
                            {
                                // Player hit opponent's cup, opponent gets more intoxicated
                                AddIntoxication(false);
                            }
                            else
                            {
                                // Opponent hit player's cup, player gets more intoxicated
                                AddIntoxication(true);
                            }

                            // Check for game state changes after cup hit
                            CheckGameStateAfterCupHit();
                            goto CONTINUE_OUTER; // break both loops
                        }
                    }
                }

                // Update the ball in the list
                balls[i] = b;

            CONTINUE_OUTER:;
            }

            // === AI OPPONENT TURN === (only in Player vs Computer mode)
            if (isPlayerVsComputer && !isPlayerTurn && !shotPending && balls.Count == 0 && currentPhase != GamePhase.GameOver)
            {
                // Simple AI: shoot at random player cup (at bottom of table)
                if (playerCups.Count > 0)
                {
                    var random = new Random();
                    var targetCup = playerCups[random.Next(playerCups.Count)];

                    // Apply NPC-specific AI accuracy modifications
                    Vector2 targetPos = targetCup.Pos;

                    if (isStoryMode && currentNPC != null)
                    {
                        // Use NPC-specific accuracy modification
                        targetPos = currentNPC.ModifyAIAccuracy(targetPos, opponentIntoxication, random);
                    }
                    else
                    {
                        // Default AI behavior for non-story mode
                        if (opponentIntoxication > 0f)
                        {
                            // Add much more random inaccuracy based on intoxication level
                            float maxInaccuracy = opponentIntoxication * 200f; // Up to 200 pixels off when fully drunk
                            float offsetX = (float)(random.NextDouble() - 0.5) * 2f * maxInaccuracy;
                            float offsetY = (float)(random.NextDouble() - 0.5) * 2f * maxInaccuracy;
                            targetPos += new Vector2(offsetX, offsetY);
                        }
                        else
                        {
                            // Even when sober, add slight inaccuracy to make AI beatable
                            float baseInaccuracy = 15f; // Small random offset even when sober
                            float offsetX = (float)(random.NextDouble() - 0.5) * 2f * baseInaccuracy;
                            float offsetY = (float)(random.NextDouble() - 0.5) * 2f * baseInaccuracy;
                            targetPos += new Vector2(offsetX, offsetY);
                        }
                    }

                    // AI shoots from top of table toward player's cups at bottom
                    Vector2 aiStartPos = new Vector2(tableRect.Center.X, tableRect.Top - 50);
                    balls.Add(new Ball(aiStartPos, targetPos));
                    shotPending = true;
                }
            }

            // === RESET INPUT STATE ===
            // Reset aiming system after ball is thrown and some time has passed
            if (shotPending && balls.Count == 0)
            {
                // Check if this was a rebuttal miss
                if (currentPhase == GamePhase.Rebuttal)
                {
                    // Rebuttal shot missed, game ends
                    EndGame(!isRebuttalPlayerTurn); // Winner is opposite of rebuttal team
                }
                else
                {
                    EndTurn();
                }
            }

            previousMouseState = mouseState;
            previousKeyboardState = keyboardState;
            base.Update(gameTime);
        }

        /// <summary>
        /// Draws the game's graphics, called once per frame.
        /// </summary>
        /// <param name="gameTime">
        /// Provides a snapshot of timing values used for rendering.
        /// </param>
        private void FireBall()
        {
            // Get intersection point from the locked line positions
            Vector2 intersectionInAimBox = new Vector2(vLinePos.X, vLinePos.Y);

            // Convert to UV coordinates (0-1 range within aim box)
            Vector2 uv = new Vector2(
                intersectionInAimBox.X / aimBox.Width,
                intersectionInAimBox.Y / aimBox.Height
            );

            // Map to opponent's cup area (top of table) for target
            Vector2 targetPos = new Vector2(
                MathHelper.Lerp(tableRect.Left, tableRect.Right, uv.X),
                MathHelper.Lerp(tableRect.Top, tableRect.Top + 150, uv.Y) // Target opponent's area at top
            );

            // Start position - from the player's side (bottom of table)
            Vector2 startPos = new Vector2(
                tableRect.Center.X, // Center horizontally
                tableRect.Bottom + 50 // Start from player's side at bottom
            );

            // Create ball with trajectory from start to target
            balls.Add(new Ball(startPos, targetPos));
        }

        private void CheckGameStateAfterCupHit()
        {
            if (currentPhase == GamePhase.MainGame)
            {
                // Check if someone won the main game (eliminated ALL opponent cups)
                if (opponentCups.Count == 0)
                {
                    // Player eliminated all opponent cups, opponent gets rebuttal chance
                    // Opponent must now hit all remaining player cups to force overtime
                    StartRebuttal(false); // false = opponent gets rebuttal
                }
                else if (playerCups.Count == 0)
                {
                    // Opponent eliminated all player cups, player gets rebuttal chance
                    // Player must now hit all remaining opponent cups to force overtime
                    StartRebuttal(true); // true = player gets rebuttal
                }
                else
                {
                    // Game continues normally - neither team eliminated all opponent cups
                    EndTurn();
                }
            }
            else if (currentPhase == GamePhase.Rebuttal)
            {
                // During rebuttal, the losing team shoots at the winning team's remaining cups
                // If player is in rebuttal, they shoot at opponent cups
                // If opponent is in rebuttal, they shoot at player cups

                if (isRebuttalPlayerTurn && opponentCups.Count == 0)
                {
                    // Player completed rebuttal successfully (hit all opponent cups), go to overtime
                    StartOvertime();
                }
                else if (!isRebuttalPlayerTurn && playerCups.Count == 0)
                {
                    // Opponent completed rebuttal successfully (hit all player cups), go to overtime
                    StartOvertime();
                }
                else
                {
                    // Cups remain, rebuttal continues - reset aiming for next shot
                    horizLocked = false;
                    shotPending = false;
                    // Don't call EndTurn() - same player shoots again
                }
            }
            else if (currentPhase == GamePhase.Overtime)
            {
                // Check for overtime winner
                if (opponentCups.Count == 0)
                {
                    // Player wins overtime and entire match
                    EndGame(true);
                }
                else if (playerCups.Count == 0)
                {
                    // Opponent wins overtime and entire match
                    EndGame(false);
                }
                else
                {
                    // Overtime continues normally
                    EndTurn();
                }
            }
        }

        private void EndTurn()
        {
            // Reset aiming system
            horizLocked = false;
            shotPending = false;

            // Decrease intoxication over time (recovery)
            playerIntoxication = Math.Max(0f, playerIntoxication - intoxicationDecay);

            float opponentDecay = intoxicationDecay;
            // Apply NPC-specific intoxication decay modifications
            if (isStoryMode && currentNPC != null)
            {
                opponentDecay = currentNPC.ModifyIntoxication(opponentDecay, true);
            }
            opponentIntoxication = Math.Max(0f, opponentIntoxication - opponentDecay);

            // Update line speed based on player's intoxication
            UpdateLineSpeed();

            // Switch turns (unless in rebuttal mode)
            if (!isRebuttalActive)
            {
                isPlayerTurn = !isPlayerTurn;
            }

            shotsThisTurn = 0;
        }

        private void StartRebuttal(bool playerGetsRebuttal)
        {
            currentPhase = GamePhase.Rebuttal;
            isRebuttalActive = true;
            isRebuttalPlayerTurn = playerGetsRebuttal;
            isPlayerTurn = playerGetsRebuttal; // Set turn to rebuttal team

            // Reset aiming system for rebuttal
            horizLocked = false;
            shotPending = false;
        }

        private void StartOvertime()
        {
            currentPhase = GamePhase.Overtime;
            isRebuttalActive = false;

            // Place 3 cups for each team
            PlaceOvertimeCups();

            // Player goes first in overtime
            isPlayerTurn = true;

            // Reset aiming system
            horizLocked = false;
            shotPending = false;

            // Don't reset intoxication - it carries over from main game
        }

        private void EndGame(bool playerWon)
        {
            currentPhase = GamePhase.GameOver;
            gameComplete = true;
            isRebuttalActive = false;

            // Handle story mode progression (prepare for next NPC but don't auto-advance)
            if (isStoryMode && playerWon)
            {
                currentNPCNumber++;
                storyModeProgress = Math.Max(storyModeProgress, currentNPCNumber);

                // Don't automatically advance - wait for player input
                // The UpdateGameplay method will handle the transition when Enter is pressed
            }

            // Reset aiming system
            horizLocked = false;
            shotPending = false;
        }

        private void ContinueStoryMode()
        {
            if (isStoryMode && currentNPCNumber <= NPCFactory.GetTotalNPCCount())
            {
                // Load the next NPC
                currentNPC = NPCFactory.GetNPC(currentNPCNumber);

                // Start next NPC battle
                InitializeGame();
            }
            else
            {
                // Story mode completed, return to menu
                currentState = GameState.MainMenu;
                selectedMenuOption = 0;
                isStoryMode = false;
                currentNPC = null;
            }
        }

        private void UpdateLineSpeed()
        {
            // Higher intoxication = much faster lines = much harder to aim
            float intoxicationMultiplier = 1f + (playerIntoxication * 5f); // 1x to 6x speed
            float baseSpeed = baseLineSpeed * intoxicationMultiplier;

            // Apply NPC-specific line speed modifications
            if (isStoryMode && currentNPC != null)
            {
                baseSpeed = currentNPC.ModifyPlayerLineSpeed(baseSpeed, playerIntoxication);
            }

            lineSpeed = baseSpeed;
        }

        private void AddIntoxication(bool toPlayer)
        {
            if (toPlayer)
            {
                float intoxicationToAdd = intoxicationPerCup;
                playerIntoxication = Math.Min(1f, playerIntoxication + intoxicationToAdd);
            }
            else
            {
                float intoxicationToAdd = intoxicationPerCup;

                // Apply NPC-specific intoxication modifications
                if (isStoryMode && currentNPC != null)
                {
                    intoxicationToAdd = currentNPC.ModifyIntoxication(intoxicationToAdd, false);
                }

                opponentIntoxication = Math.Min(1f, opponentIntoxication + intoxicationToAdd);
            }

            // Update line speed immediately if it affects the player
            if (toPlayer)
            {
                UpdateLineSpeed();
            }
        }

        protected override void Draw(GameTime gameTime)
        {
            GraphicsDevice.Clear(Color.Black);
            spriteBatch.Begin();

            if (currentState == GameState.MainMenu)
            {
                DrawMainMenu();
            }
            else if (currentState == GameState.Playing)
            {
                DrawGameplay();
            }

            spriteBatch.End();
        }

        private void DrawMainMenu()
        {
            // Title
            string title = "SUPER BEER PONG";
            Vector2 titleSize = hudFont.MeasureString(title);
            Vector2 titlePos = new Vector2((GraphicsDevice.Viewport.Width - titleSize.X) / 2, 100);
            spriteBatch.DrawString(hudFont, title, titlePos, Color.Yellow);

            // Menu options
            string[] menuOptions = { "Story Mode", "Player vs Computer", "Player vs Player", "Exit" };
            Vector2 menuStart = new Vector2(GraphicsDevice.Viewport.Width / 2, 220);

            for (int i = 0; i < menuOptions.Length; i++)
            {
                Color optionColor = (i == selectedMenuOption) ? Color.White : Color.Gray;
                string optionText = (i == selectedMenuOption) ? $"> {menuOptions[i]} <" : menuOptions[i];
                Vector2 optionSize = hudFont.MeasureString(optionText);
                Vector2 optionPos = new Vector2(menuStart.X - optionSize.X / 2, menuStart.Y + i * 50);
                spriteBatch.DrawString(hudFont, optionText, optionPos, optionColor);
            }

            // Instructions
            string instructions = "Use UP/DOWN arrows and ENTER to select, or ESC to exit";
            Vector2 instructSize = hudFont.MeasureString(instructions);
            Vector2 instructPos = new Vector2((GraphicsDevice.Viewport.Width - instructSize.X) / 2, 450);
            spriteBatch.DrawString(hudFont, instructions, instructPos, Color.LightGray);
        }

        private void DrawGameplay()
        {
            // Draw UI first (behind table)
            if (isStoryMode && currentNPC != null)
            {
                DrawStoryModeUI();
            }
            else
            {
                DrawStandardUI();
            }

            // table
            spriteBatch.Draw(texTable, tableRect, Color.White);

            // cups (scale to ~40px) - same sprite for both teams
            foreach (var cup in playerCups)
                DrawCentered(texCup, cup.Pos, 40); // Player's cups
            foreach (var cup in opponentCups)
                DrawCentered(texCup, cup.Pos, 40); // Opponent's cups

            // balls (with dynamic scaling for depth)
            foreach (var ball in balls)
            {
                // Draw shadow on table (darker, smaller)
                Vector2 shadowPos = ball.Pos;
                int shadowSize = (int)(ball.GetScale() * 0.6f);
                DrawCentered(texBall, shadowPos, shadowSize, Color.Black * 0.3f);

                // Draw ball with height offset for 3D effect
                Vector2 ballPos = ball.Pos - new Vector2(0, ball.Z * 0.5f); // Offset based on height
                DrawCentered(texBall, ballPos, (int)ball.GetScale(), Color.White);
            }

            // aim box background (semi transparent green)
            spriteBatch.Draw(texPixel, aimBox, Color.Green * 0.3f);

            // aim box border (white outline)
            DrawRectangleBorder(aimBox, Color.White, 2);

            // horizontal line (vertical line that moves left/right)
            int hLineX = aimBox.X + (int)hLinePos.X;
            spriteBatch.Draw(texPixel,
                new Rectangle(hLineX, aimBox.Y, 5, aimBox.Height),
                Color.Red);

            // vertical line (horizontal line that moves up/down, only after first click)
            if (horizLocked)
            {
                int vLineY = aimBox.Y + (int)vLinePos.Y;
                spriteBatch.Draw(texPixel,
                    new Rectangle(aimBox.X, vLineY, aimBox.Width, 5),
                    Color.Blue);
            }


        }

        private void DrawStoryModeUI()
        {
            // Profile image in upper left corner - same size as aiming controller (180x180)
            int profileSize = 180;
            Rectangle profileRect = new Rectangle(20, 20, profileSize, profileSize);

            // Get the appropriate profile texture for this NPC
            if (profileTextures.TryGetValue(currentNPC.ProfileImageName, out Texture2D profileTexture))
            {
                spriteBatch.Draw(profileTexture, profileRect, Color.White);
            }
            else
            {
                // Fallback: Draw a placeholder rectangle if texture not found
                spriteBatch.Draw(texPixel, profileRect, Color.Gray);
            }

            // Profile border
            DrawRectangleBorder(profileRect, Color.White, 2);

            // Define text box area under the profile image
            int textBoxX = 20; // Start at same X as profile
            int textBoxY = profileRect.Bottom + 10; // Start 10px below profile
            int textBoxWidth = tableRect.Left - textBoxX - 20; // Width until table starts (with 20px margin)
            int textBoxHeight = tableRect.Top - textBoxY - 10; // Height until table starts (with 10px margin)
            Rectangle textBoxRect = new Rectangle(textBoxX, textBoxY, textBoxWidth, textBoxHeight);

            // Draw text box border for debugging (optional - can be removed)
            // DrawRectangleBorder(textBoxRect, Color.Gray, 1);

            int lineHeight = 18; // Smaller line height to fit more content
            int currentY = textBoxY + 5; // Start with small margin inside text box

            // Story mode title (compact)
            string storyTitle = $"NPC {currentNPCNumber}/{NPCFactory.GetTotalNPCCount()}: {currentNPC.Name}";
            currentY = DrawWrappedText(storyTitle, textBoxX + 5, currentY, textBoxWidth - 10, lineHeight, Color.Yellow);
            currentY += 5; // Small gap

            // NPC description (with wrapping)
            string npcDesc = currentNPC.Description;
            currentY = DrawWrappedText(npcDesc, textBoxX + 5, currentY, textBoxWidth - 10, lineHeight, Color.LightBlue);
            currentY += 5; // Small gap

            // Special ability (with wrapping)
            string ability = $"Ability: {currentNPC.SpecialAbility}";
            currentY = DrawWrappedText(ability, textBoxX + 5, currentY, textBoxWidth - 10, lineHeight, Color.Orange);
            currentY += 5; // Small gap

            // Cup counts
            string cupCounts = $"Cups - You: {playerCups.Count} | {currentNPC.Name}: {opponentCups.Count}";
            currentY = DrawWrappedText(cupCounts, textBoxX + 5, currentY, textBoxWidth - 10, lineHeight, Color.White);
            currentY += 5; // Small gap

            // Intoxication levels (compact)
            string intoxLevels = $"Intox - You: {playerIntoxication:P0} | {currentNPC.Name}: {opponentIntoxication:P0}";
            Color intoxColor = playerIntoxication > 0.5f ? Color.Red : Color.White;
            currentY = DrawWrappedText(intoxLevels, textBoxX + 5, currentY, textBoxWidth - 10, lineHeight, intoxColor);
            currentY += 5; // Small gap

            // Game phase status
            string phaseText = GetGamePhaseText();
            Color phaseColor = GetGamePhaseColor();
            currentY = DrawWrappedText(phaseText, textBoxX + 5, currentY, textBoxWidth - 10, lineHeight, phaseColor);
            currentY += 5; // Small gap

            // Aiming instructions (if applicable)
            if ((isPlayerVsComputer && isPlayerTurn) || (!isPlayerVsComputer))
            {
                string aimingText = GetAimingInstructions();
                currentY = DrawWrappedText(aimingText, textBoxX + 5, currentY, textBoxWidth - 10, lineHeight, Color.Yellow);

                // Intoxication warning
                if (isPlayerTurn && playerIntoxication > 0.15f)
                {
                    currentY += 5; // Small gap
                    string drunkText = "*** Tipsy! Lines faster! ***";
                    currentY = DrawWrappedText(drunkText, textBoxX + 5, currentY, textBoxWidth - 10, lineHeight, Color.Orange);
                }
            }
        }

        private void DrawStandardUI()
        {
            // Standard UI layout for non-story mode
            string modeText = isPlayerVsComputer ? "Player vs Computer" : "Player vs Player";
            spriteBatch.DrawString(hudFont, modeText, new Vector2(10, 10), Color.Cyan);

            int yOffset = 40;

            // Cup counts
            string scoreText;
            if (currentPhase == GamePhase.Overtime)
            {
                scoreText = $"OVERTIME - Player 1 Cups: {playerCups.Count} | Player 2 Cups: {opponentCups.Count}";
            }
            else
            {
                string player1Label = "Player 1 (bottom)";
                string player2Label = isPlayerVsComputer ? "Computer (top)" : "Player 2 (top)";
                scoreText = $"{player1Label}: {playerCups.Count} | {player2Label}: {opponentCups.Count}";
            }
            spriteBatch.DrawString(hudFont, scoreText, new Vector2(10, yOffset), Color.White);
            yOffset += 30;

            // Intoxication levels
            string p1Label = "Player 1";
            string p2Label = isPlayerVsComputer ? "Computer" : "Player 2";
            string intoxText = $"{p1Label} Intoxication: {playerIntoxication:P0} | {p2Label}: {opponentIntoxication:P0} | Line Speed: {Math.Abs(lineSpeed):F0}";
            Color intoxColor = playerIntoxication > 0.5f ? Color.Red : Color.White;
            spriteBatch.DrawString(hudFont, intoxText, new Vector2(10, yOffset), intoxColor);
            yOffset += 30;

            // Game phase and turn indicator
            string turnText = GetGamePhaseText();
            Color turnColor = GetGamePhaseColor();
            spriteBatch.DrawString(hudFont, turnText, new Vector2(10, yOffset), turnColor);
            yOffset += 30;

            // Aiming instructions
            if ((isPlayerVsComputer && isPlayerTurn) || (!isPlayerVsComputer))
            {
                string aimingText = GetAimingInstructions();
                spriteBatch.DrawString(hudFont, aimingText, new Vector2(10, yOffset), Color.Yellow);
                yOffset += 30;

                // Intoxication warning
                if (isPlayerTurn && playerIntoxication > 0.15f)
                {
                    string drunkText = "*** You're feeling tipsy! Lines are moving faster! ***";
                    spriteBatch.DrawString(hudFont, drunkText, new Vector2(10, yOffset), Color.Orange);
                }
                else if (!isPlayerTurn && !isPlayerVsComputer && opponentIntoxication > 0.15f)
                {
                    string drunkText = "*** Player 2 is feeling tipsy! Lines are moving faster! ***";
                    spriteBatch.DrawString(hudFont, drunkText, new Vector2(10, yOffset), Color.Orange);
                }
            }
        }

        private string GetGamePhaseText()
        {
            if (currentPhase == GamePhase.Rebuttal)
            {
                if (isRebuttalPlayerTurn)
                {
                    return $"REBUTTAL! Hit all {opponentCups.Count} cups or lose!";
                }
                else
                {
                    string opponentName = (isStoryMode && currentNPC != null) ? currentNPC.Name : "Opponent";
                    return $"{opponentName} REBUTTAL - Hit all {playerCups.Count} cups!";
                }
            }
            else if (currentPhase == GamePhase.GameOver)
            {
                if (playerCups.Count == 0 && opponentCups.Count > 0)
                {
                    string opponentName = (isStoryMode && currentNPC != null) ? currentNPC.Name : "Opponent";
                    if (isStoryMode)
                    {
                        return $"{opponentName} WINS! Press ENTER to retry";
                    }
                    else
                    {
                        return $"{opponentName} WINS! Press ENTER to play again";
                    }
                }
                else if (opponentCups.Count == 0 && playerCups.Count > 0)
                {
                    if (isStoryMode)
                    {
                        if (currentNPCNumber <= NPCFactory.GetTotalNPCCount())
                        {
                            return "YOU WIN! Press ENTER for next NPC";
                        }
                        else
                        {
                            return "STORY MODE COMPLETE! Press ENTER to return to menu";
                        }
                    }
                    else
                    {
                        return "YOU WIN! Press ENTER to play again";
                    }
                }
                else
                {
                    return "GAME OVER - Press ENTER to continue";
                }
            }
            else
            {
                if (isStoryMode && currentNPC != null)
                {
                    return isPlayerTurn ? "YOUR TURN - Aim at top!" : $"{currentNPC.Name}'S TURN";
                }
                else if (isPlayerVsComputer)
                {
                    return isPlayerTurn ? "YOUR TURN - Aim at top!" : "COMPUTER'S TURN";
                }
                else
                {
                    return isPlayerTurn ? "PLAYER 1'S TURN" : "PLAYER 2'S TURN";
                }
            }
        }

        private Color GetGamePhaseColor()
        {
            if (currentPhase == GamePhase.Rebuttal)
            {
                return isRebuttalPlayerTurn ? Color.Yellow : Color.Orange;
            }
            else if (currentPhase == GamePhase.GameOver)
            {
                if (playerCups.Count == 0 && opponentCups.Count > 0)
                    return Color.Red;
                else if (opponentCups.Count == 0 && playerCups.Count > 0)
                    return Color.Green;
                else
                    return Color.White;
            }
            else
            {
                return isPlayerTurn ? Color.Green : Color.Red;
            }
        }

        private string GetAimingInstructions()
        {
            if (!horizLocked)
                return "Phase 1: Click to lock horizontal line";
            else if (!shotPending)
                return "Phase 2: Click to lock vertical line and fire";
            else
                return "Ball in flight...";
        }

        /// <summary>
        /// Draws text with automatic wrapping within the specified width
        /// Returns the Y position after the last line of text
        /// </summary>
        private int DrawWrappedText(string text, int x, int y, int maxWidth, int lineHeight, Color color)
        {
            if (string.IsNullOrEmpty(text))
                return y;

            string[] words = text.Split(' ');
            string currentLine = "";
            int currentY = y;

            foreach (string word in words)
            {
                string testLine = string.IsNullOrEmpty(currentLine) ? word : currentLine + " " + word;
                Vector2 testSize = hudFont.MeasureString(testLine);

                if (testSize.X <= maxWidth)
                {
                    // Word fits on current line
                    currentLine = testLine;
                }
                else
                {
                    // Word doesn't fit, draw current line and start new one
                    if (!string.IsNullOrEmpty(currentLine))
                    {
                        spriteBatch.DrawString(hudFont, currentLine, new Vector2(x, currentY), color);
                        currentY += lineHeight;
                    }
                    currentLine = word;

                    // Check if single word is too long for the line
                    Vector2 wordSize = hudFont.MeasureString(word);
                    if (wordSize.X > maxWidth)
                    {
                        // Word is too long, we'll draw it anyway but it will overflow
                        spriteBatch.DrawString(hudFont, word, new Vector2(x, currentY), color);
                        currentY += lineHeight;
                        currentLine = "";
                    }
                }
            }

            // Draw the last line if there's content
            if (!string.IsNullOrEmpty(currentLine))
            {
                spriteBatch.DrawString(hudFont, currentLine, new Vector2(x, currentY), color);
                currentY += lineHeight;
            }

            return currentY;
        }

        // helper - draw texture centered at position with specified size
        private void DrawCentered(Texture2D tex, Vector2 pos, int size, Color? color = null)
        {
            var destRect = new Rectangle(
                (int)(pos.X - size / 2f),
                (int)(pos.Y - size / 2f),
                size,
                size
            );
            spriteBatch.Draw(tex, destRect, color ?? Color.White);
        }

        // helper - draw rectangle border
        private void DrawRectangleBorder(Rectangle rectangle, Color color, int thickness)
        {
            // Top
            spriteBatch.Draw(texPixel, new Rectangle(rectangle.X, rectangle.Y, rectangle.Width, thickness), color);
            // Bottom
            spriteBatch.Draw(texPixel, new Rectangle(rectangle.X, rectangle.Bottom - thickness, rectangle.Width, thickness), color);
            // Left
            spriteBatch.Draw(texPixel, new Rectangle(rectangle.X, rectangle.Y, thickness, rectangle.Height), color);
            // Right
            spriteBatch.Draw(texPixel, new Rectangle(rectangle.Right - thickness, rectangle.Y, thickness, rectangle.Height), color);
        }
    }




}