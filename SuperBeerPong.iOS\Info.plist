<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>MinimumOSVersion</key>
	<string>12.2</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CFBundleName</key>
	<string>SuperBeerPong</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarHidden</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
		<integer>2</integer>
	</array>
	<key>CFBundleIdentifier</key>
	<string>com.companyname.SuperBeerPong</string>
	<key>CFBundleDisplayName</key>
	<string>SuperBeerPong</string>
	<key>XSAppIconAssets</key>
	<string>AppIcon.xcassets/AppIcon.appiconset</string>
</dict>
</plist>
