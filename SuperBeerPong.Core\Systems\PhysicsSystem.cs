using Microsoft.Xna.Framework;
using SuperBeerPong.Core.Core;
using System;
using System.Collections.Generic;

namespace SuperBeerPong.Core.Systems
{
    /// <summary>
    /// Manages ball physics and collision detection
    /// </summary>
    public class PhysicsSystem
    {
        private List<Ball> balls;
        private Rectangle tableBounds;

        // Events
        public event Action<Ball, Cup> BallHitCup;
        public event Action<Ball> BallOutOfBounds;
        public event Action<Ball> BallLanded;

        // Properties
        public int ActiveBallCount => balls.Count;
        public bool HasActiveBalls => balls.Count > 0;

        public PhysicsSystem()
        {
            balls = new List<Ball>();
        }

        /// <summary>
        /// Initialize the physics system with table bounds
        /// </summary>
        public void Initialize(Rectangle tableBounds)
        {
            this.tableBounds = tableBounds;
            balls.Clear();
        }

        /// <summary>
        /// Update all balls and check for collisions
        /// </summary>
        public void Update(GameTime gameTime, List<Cup> playerCups, List<Cup> opponentCups)
        {
            float deltaTime = (float)gameTime.ElapsedGameTime.TotalSeconds;

            // Update balls in reverse order to safely remove them
            for (int i = balls.Count - 1; i >= 0; i--)
            {
                Ball ball = balls[i];
                ball.Update(deltaTime);

                // Check if ball is still active
                if (!ball.IsActive())
                {
                    // Ball has completed its flight
                    BallLanded?.Invoke(ball);
                    balls.RemoveAt(i);
                    continue;
                }

                // Check for out of bounds
                if (ball.IsOutOfBounds(tableBounds))
                {
                    BallOutOfBounds?.Invoke(ball);
                    balls.RemoveAt(i);
                    continue;
                }

                // Check for cup collisions (only when ball is close to table level)
                if (ball.CanHitCups())
                {
                    bool hitCup = false;

                    // Check collision with target cups based on who shot the ball
                    List<Cup> targetCups = ball.IsPlayerBall ? opponentCups : playerCups;

                    for (int c = targetCups.Count - 1; c >= 0; c--)
                    {
                        Cup cup = targetCups[c];
                        if (cup.IsActive && ball.CheckCollisionWithCup(cup))
                        {
                            // Ball hit a cup
                            BallHitCup?.Invoke(ball, cup);
                            balls.RemoveAt(i);
                            hitCup = true;
                            break;
                        }
                    }

                    if (hitCup) continue;
                }

                // Update the ball in the list
                balls[i] = ball;
            }
        }

        /// <summary>
        /// Add a new ball to the physics system
        /// </summary>
        public void AddBall(Vector2 startPos, Vector2 targetPos, bool isPlayerBall = true)
        {
            Ball newBall = new Ball(startPos, targetPos, isPlayerBall);
            balls.Add(newBall);
        }

        /// <summary>
        /// Add a ball with accuracy variation (for AI shots)
        /// </summary>
        public void AddBallWithVariation(Vector2 startPos, Vector2 targetPos, float inaccuracy, Random random, bool isPlayerBall = false)
        {
            Ball newBall = Ball.CreateWithVariation(startPos, targetPos, inaccuracy, random, isPlayerBall);
            balls.Add(newBall);
        }

        /// <summary>
        /// Get all active balls
        /// </summary>
        public List<Ball> GetActiveBalls()
        {
            return new List<Ball>(balls);
        }

        /// <summary>
        /// Clear all balls
        /// </summary>
        public void ClearAllBalls()
        {
            balls.Clear();
        }

        /// <summary>
        /// Get the most recent ball (last shot)
        /// </summary>
        public Ball? GetMostRecentBall()
        {
            return balls.Count > 0 ? balls[balls.Count - 1] : null;
        }

        /// <summary>
        /// Get all player balls
        /// </summary>
        public List<Ball> GetPlayerBalls()
        {
            List<Ball> playerBalls = new List<Ball>();
            foreach (Ball ball in balls)
            {
                if (ball.IsPlayerBall)
                    playerBalls.Add(ball);
            }
            return playerBalls;
        }

        /// <summary>
        /// Get all opponent balls
        /// </summary>
        public List<Ball> GetOpponentBalls()
        {
            List<Ball> opponentBalls = new List<Ball>();
            foreach (Ball ball in balls)
            {
                if (!ball.IsPlayerBall)
                    opponentBalls.Add(ball);
            }
            return opponentBalls;
        }

        /// <summary>
        /// Check if any balls are currently in flight
        /// </summary>
        public bool AnyBallsInFlight()
        {
            return balls.Count > 0;
        }

        /// <summary>
        /// Get the trajectory points for the most recent ball (for visualization)
        /// </summary>
        public Vector2[] GetMostRecentTrajectory(int pointCount = 20)
        {
            Ball? recentBall = GetMostRecentBall();
            if (recentBall.HasValue)
            {
                return recentBall.Value.GetTrajectoryPoints(pointCount);
            }
            return new Vector2[0];
        }

        /// <summary>
        /// Predict where a ball will land based on its trajectory
        /// </summary>
        public Vector2 PredictLandingPosition(Vector2 startPos, Vector2 targetPos)
        {
            // For now, just return the target position
            // Could be enhanced with more complex physics simulation
            return targetPos;
        }

        /// <summary>
        /// Check if a position is a valid target (within table bounds)
        /// </summary>
        public bool IsValidTarget(Vector2 position)
        {
            return tableBounds.Contains(position);
        }

        /// <summary>
        /// Get the closest cup to a position
        /// </summary>
        public Cup? GetClosestCup(Vector2 position, List<Cup> cups)
        {
            Cup? closestCup = null;
            float closestDistance = float.MaxValue;

            foreach (Cup cup in cups)
            {
                if (!cup.IsActive) continue;

                float distance = Vector2.Distance(position, cup.Pos);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closestCup = cup;
                }
            }

            return closestCup;
        }

        /// <summary>
        /// Calculate optimal target position for hitting a specific cup
        /// </summary>
        public Vector2 CalculateOptimalTarget(Vector2 startPos, Cup targetCup)
        {
            // Simple direct targeting - could be enhanced with arc calculation
            return targetCup.Pos;
        }

        /// <summary>
        /// Apply physics effects like wind or table tilt (for advanced gameplay)
        /// </summary>
        public Vector2 ApplyPhysicsEffects(Vector2 targetPos, float windStrength = 0f, Vector2 windDirection = default)
        {
            // Apply wind effect
            Vector2 adjustedTarget = targetPos + (windDirection * windStrength);
            
            // Ensure target stays within reasonable bounds
            adjustedTarget.X = Math.Clamp(adjustedTarget.X, tableBounds.Left, tableBounds.Right);
            adjustedTarget.Y = Math.Clamp(adjustedTarget.Y, tableBounds.Top, tableBounds.Bottom);
            
            return adjustedTarget;
        }

        /// <summary>
        /// Set table bounds for collision detection
        /// </summary>
        public void SetTableBounds(Rectangle bounds)
        {
            tableBounds = bounds;
        }

        /// <summary>
        /// Get current table bounds
        /// </summary>
        public Rectangle GetTableBounds()
        {
            return tableBounds;
        }

        /// <summary>
        /// Get debug information about the physics system
        /// </summary>
        public string GetDebugInfo()
        {
            int playerBalls = GetPlayerBalls().Count;
            int opponentBalls = GetOpponentBalls().Count;
            return $"Total Balls: {balls.Count}, Player: {playerBalls}, Opponent: {opponentBalls}";
        }

        /// <summary>
        /// Check if any balls are close to landing (for timing effects)
        /// </summary>
        public bool AnyBallsNearLanding(float timeThreshold = 0.2f)
        {
            foreach (Ball ball in balls)
            {
                float remainingTime = ball.MaxFlightTime - ball.FlightTime;
                if (remainingTime <= timeThreshold)
                    return true;
            }
            return false;
        }

        /// <summary>
        /// Get the average position of all active balls
        /// </summary>
        public Vector2 GetAverageBallPosition()
        {
            if (balls.Count == 0) return Vector2.Zero;

            Vector2 sum = Vector2.Zero;
            foreach (Ball ball in balls)
            {
                sum += ball.Pos;
            }
            return sum / balls.Count;
        }
    }
}
