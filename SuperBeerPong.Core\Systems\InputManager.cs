using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Input;
using SuperBeerPong.Core.Core;
using SuperBeerPong.Core.Core.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SuperBeerPong.Core.Systems
{
    /// <summary>
    /// Centralized input handling system for the game
    /// </summary>
    public class InputManager : IInputHandler
    {
        private KeyboardState currentKeyboardState;
        private KeyboardState previousKeyboardState;
        private MouseState currentMouseState;
        private MouseState previousMouseState;
        
        private Dictionary<InputAction, InputMapping> inputMappings;
        private HashSet<Keys> pressedKeysThisFrame;

        // Events
        public event Action<Keys> KeyPressed;
        public event Action<Vector2> MouseClicked;
        public event Action<InputAction> ActionTriggered;

        // Properties
        public KeyboardState CurrentKeyboardState => currentKeyboardState;
        public KeyboardState PreviousKeyboardState => previousKeyboardState;
        public MouseState CurrentMouseState => currentMouseState;
        public MouseState PreviousMouseState => previousMouseState;

        public InputManager()
        {
            inputMappings = DefaultInputMappings.GetMappingDictionary();
            pressedKeysThisFrame = new HashSet<Keys>();
        }

        /// <summary>
        /// Update input states and detect input events
        /// </summary>
        public void Update(GameTime gameTime)
        {
            // Store previous states
            previousKeyboardState = currentKeyboardState;
            previousMouseState = currentMouseState;

            // Get current states
            currentKeyboardState = Keyboard.GetState();
            currentMouseState = Mouse.GetState();

            // Detect key presses and trigger events
            DetectKeyPresses();
            DetectMouseClicks();
            DetectActionTriggers();
        }

        /// <summary>
        /// Check if a key was just pressed (down this frame, up last frame)
        /// </summary>
        public bool IsKeyPressed(Keys key)
        {
            return currentKeyboardState.IsKeyDown(key) && !previousKeyboardState.IsKeyDown(key);
        }

        /// <summary>
        /// Check if a key is currently held down
        /// </summary>
        public bool IsKeyDown(Keys key)
        {
            return currentKeyboardState.IsKeyDown(key);
        }

        /// <summary>
        /// Check if a key was just released
        /// </summary>
        public bool IsKeyReleased(Keys key)
        {
            return !currentKeyboardState.IsKeyDown(key) && previousKeyboardState.IsKeyDown(key);
        }

        /// <summary>
        /// Check if left mouse button was just clicked
        /// </summary>
        public bool IsLeftMousePressed()
        {
            return currentMouseState.LeftButton == ButtonState.Pressed && 
                   previousMouseState.LeftButton == ButtonState.Released;
        }

        /// <summary>
        /// Check if left mouse button is currently held
        /// </summary>
        public bool IsLeftMouseDown()
        {
            return currentMouseState.LeftButton == ButtonState.Pressed;
        }

        /// <summary>
        /// Check if right mouse button was just clicked
        /// </summary>
        public bool IsRightMousePressed()
        {
            return currentMouseState.RightButton == ButtonState.Pressed && 
                   previousMouseState.RightButton == ButtonState.Released;
        }

        /// <summary>
        /// Get current mouse position
        /// </summary>
        public Vector2 GetMousePosition()
        {
            return new Vector2(currentMouseState.X, currentMouseState.Y);
        }

        /// <summary>
        /// Get mouse movement delta since last frame
        /// </summary>
        public Vector2 GetMouseDelta()
        {
            return new Vector2(
                currentMouseState.X - previousMouseState.X,
                currentMouseState.Y - previousMouseState.Y
            );
        }

        /// <summary>
        /// Check if a specific input action was triggered
        /// </summary>
        public bool IsActionTriggered(InputAction action)
        {
            if (!inputMappings.TryGetValue(action, out InputMapping mapping))
                return false;

            bool primaryTriggered = mapping.PrimaryKey != Keys.None && IsKeyPressed(mapping.PrimaryKey);
            bool secondaryTriggered = mapping.SecondaryKey != Keys.None && IsKeyPressed(mapping.SecondaryKey);

            bool triggered = primaryTriggered || secondaryTriggered;

            // Check modifier requirements
            if (triggered && mapping.RequireShift)
                triggered = IsKeyDown(Keys.LeftShift) || IsKeyDown(Keys.RightShift);
            
            if (triggered && mapping.RequireCtrl)
                triggered = IsKeyDown(Keys.LeftControl) || IsKeyDown(Keys.RightControl);
            
            if (triggered && mapping.RequireAlt)
                triggered = IsKeyDown(Keys.LeftAlt) || IsKeyDown(Keys.RightAlt);

            return triggered;
        }

        /// <summary>
        /// Check if a specific input action is currently active
        /// </summary>
        public bool IsActionActive(InputAction action)
        {
            if (!inputMappings.TryGetValue(action, out InputMapping mapping))
                return false;

            bool primaryActive = mapping.PrimaryKey != Keys.None && IsKeyDown(mapping.PrimaryKey);
            bool secondaryActive = mapping.SecondaryKey != Keys.None && IsKeyDown(mapping.SecondaryKey);

            return primaryActive || secondaryActive;
        }

        /// <summary>
        /// Map a key to an input action
        /// </summary>
        public void MapKeyToAction(Keys key, InputAction action)
        {
            if (inputMappings.ContainsKey(action))
            {
                var mapping = inputMappings[action];
                mapping.PrimaryKey = key;
                inputMappings[action] = mapping;
            }
            else
            {
                inputMappings[action] = new InputMapping(action, key);
            }
        }

        /// <summary>
        /// Get all currently pressed keys
        /// </summary>
        public Keys[] GetPressedKeys()
        {
            return currentKeyboardState.GetPressedKeys();
        }

        /// <summary>
        /// Check if any key was pressed this frame
        /// </summary>
        public bool AnyKeyPressed()
        {
            return pressedKeysThisFrame.Count > 0;
        }

        /// <summary>
        /// Add a custom input mapping
        /// </summary>
        public void AddMapping(InputMapping mapping)
        {
            inputMappings[mapping.Action] = mapping;
        }

        /// <summary>
        /// Remove an input mapping
        /// </summary>
        public void RemoveMapping(InputAction action)
        {
            inputMappings.Remove(action);
        }

        /// <summary>
        /// Get current mapping for an action
        /// </summary>
        public InputMapping? GetMapping(InputAction action)
        {
            return inputMappings.TryGetValue(action, out InputMapping mapping) ? mapping : null;
        }

        /// <summary>
        /// Reset all mappings to defaults
        /// </summary>
        public void ResetToDefaults()
        {
            inputMappings = DefaultInputMappings.GetMappingDictionary();
        }

        /// <summary>
        /// Detect key presses and fire events
        /// </summary>
        private void DetectKeyPresses()
        {
            pressedKeysThisFrame.Clear();
            
            var currentKeys = currentKeyboardState.GetPressedKeys();
            var previousKeys = previousKeyboardState.GetPressedKeys();

            foreach (var key in currentKeys)
            {
                if (!previousKeys.Contains(key))
                {
                    pressedKeysThisFrame.Add(key);
                    KeyPressed?.Invoke(key);
                }
            }
        }

        /// <summary>
        /// Detect mouse clicks and fire events
        /// </summary>
        private void DetectMouseClicks()
        {
            if (IsLeftMousePressed())
            {
                MouseClicked?.Invoke(GetMousePosition());
            }
        }

        /// <summary>
        /// Detect action triggers and fire events
        /// </summary>
        private void DetectActionTriggers()
        {
            foreach (var action in Enum.GetValues<InputAction>())
            {
                if (IsActionTriggered(action))
                {
                    ActionTriggered?.Invoke(action);
                }
            }
        }

        /// <summary>
        /// Check if escape key was pressed (common exit condition)
        /// </summary>
        public bool IsExitRequested()
        {
            return IsActionTriggered(InputAction.Exit) || IsKeyPressed(Keys.Escape);
        }

        /// <summary>
        /// Check for menu navigation inputs
        /// </summary>
        public int GetMenuNavigation()
        {
            if (IsActionTriggered(InputAction.MenuUp)) return -1;
            if (IsActionTriggered(InputAction.MenuDown)) return 1;
            return 0;
        }

        /// <summary>
        /// Check if menu selection was triggered
        /// </summary>
        public bool IsMenuSelectTriggered()
        {
            return IsActionTriggered(InputAction.MenuSelect);
        }

        /// <summary>
        /// Check if menu back was triggered
        /// </summary>
        public bool IsMenuBackTriggered()
        {
            return IsActionTriggered(InputAction.MenuBack);
        }
    }
}
